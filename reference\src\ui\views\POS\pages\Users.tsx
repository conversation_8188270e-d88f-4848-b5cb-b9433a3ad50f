import React, { useState, useEffect, useContext } from "react";
import {
  Box,
  Flex,
  Text,
  Input,
  Button,
  Avatar,
  Stack,
  useColorModeValue,
} from "@chakra-ui/react";
import { useGetEmployeesQuery } from "../../../generated/graphql";
import { Link } from "react-router-dom";
import { hasPermission } from "../../../interfaces/Helpers";
import { MeContext } from "../../../components/Wrapper";

const EmployeePage: React.FC = () => {
  const bgColor = useColorModeValue("gray.100", "gray.800");
  const cardBg = useColorModeValue("white", "gray.700");
  const textColor = useColorModeValue("gray.700", "white");
  const highlightColor = useColorModeValue("teal.500", "teal.300");
  const me = useContext(MeContext);

  const [{ data, fetching }] = useGetEmployeesQuery({
    requestPolicy: "network-only",
  });
  const [searchTerm, setSearchTerm] = useState("");
  const [employees, setEmployees] = useState(data?.getEmployees || []);

  useEffect(() => {
    if (data?.getEmployees) {
      setEmployees(data.getEmployees);
    }
  }, [data]);

  const filteredEmployees = employees.filter((employee) =>
    `${employee.firstname} ${employee.lastname}`
      .toLowerCase()
      .includes(searchTerm.toLowerCase())
  );

  return (
    <Box
      bg={bgColor}
      minH="85vh"
      p={5}
      flex="1" // Take remaining vertical space
      overflowY="auto"
      maxHeight={{ base: "calc(100vh - 100px)", md: "calc(100vh - 450px)" }}
      sx={{
        "&::-webkit-scrollbar": {
          width: "1px",
        },
        "&::-webkit-scrollbar-thumb": {
          background: "gray.300",
          borderRadius: "4px",
        },
        "&::-webkit-scrollbar-thumb:hover": {
          background: "gray.500",
        },
      }}
    >
      <Flex justify="center" align="center" mb={10}>
        <Text
          fontSize="3xl"
          fontWeight="extrabold"
          textAlign="center"
          mb="12px"
          bgGradient="linear(to-r, teal.400, blue.500)"
          bgClip="text"
          textShadow="1px 1px 3px rgba(0, 0, 0, 0.2)"
          p={2}
        >
          Employees
        </Text>
      </Flex>

      <Flex justify="space-between" align="center" mb={5}>
        <Input
          placeholder="Search by name"
          size="md"
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          maxW="300px"
          bg={cardBg}
        />
        {hasPermission(me?.permissions, ["Users>Add", "Users>Edit"]) && (
          <Button
            as={Link}
            to="add-employee"
            colorScheme="teal"
            size="md"
            _hover={{ bg: highlightColor }}
          >
            Add Employee
          </Button>
        )}
      </Flex>

      <Flex wrap="wrap" gap={6} justify="center">
        {fetching ? (
          <Text>Loading...</Text>
        ) : filteredEmployees.length > 0 ? (
          filteredEmployees.map((employee) => (
            <Box
              key={employee.id}
              bg={cardBg}
              p={4}
              borderRadius="md"
              shadow="md"
              w="300px"
            >
              <Stack align="center" spacing={3}>
                <Avatar
                  size="xl"
                  name={`${employee.firstname} ${employee.lastname}`}
                />
                <Text fontWeight="bold" fontSize="lg" color={textColor}>
                  {employee.firstname} {employee.lastname}
                </Text>
                <Text fontSize="sm" color="gray.500">
                  Role: {employee.role?.name || "N/A"}
                </Text>
                <Text fontSize="sm" color="gray.500">
                  Email: {employee.email}
                </Text>
                <Text fontSize="sm" color="gray.500">
                  Phone: {employee.phone || "N/A"}
                </Text>
                <Button
                  as={Link}
                  to={{
                    pathname: `employee`,
                    state: {
                      employee,
                    },
                  }}
                  colorScheme="teal"
                  size="sm"
                  _hover={{ bg: highlightColor }}
                >
                  View Details
                </Button>
              </Stack>
            </Box>
          ))
        ) : (
          <Box
            bg={cardBg}
            p={4}
            borderRadius="md"
            shadow="md"
            w="300px"
            textAlign="center"
          >
            <Text fontWeight="bold" fontSize="lg" color={textColor}>
              No Employees Found
            </Text>
            {hasPermission(me?.permissions, ["Users>Add", "Users>Edit"]) && (
              <Button
                as={Link}
                to="add-employee"
                colorScheme="teal"
                size="sm"
                mt={3}
                _hover={{ bg: highlightColor }}
              >
                Add Employee
              </Button>
            )}
          </Box>
        )}

        {hasPermission(me?.permissions, ["Users>Add", "Users>Edit"]) &&
          !fetching && (
            <Box
              bg={cardBg}
              p={4}
              borderRadius="md"
              shadow="md"
              w="300px"
              textAlign="center"
            >
              <Stack align="center" spacing={3}>
                <Text fontWeight="bold" fontSize="lg" color={textColor}>
                  Add Employee
                </Text>
                <Button
                  as={Link}
                  to="add-employee"
                  colorScheme="teal"
                  size="sm"
                  _hover={{ bg: highlightColor }}
                >
                  Add Employee
                </Button>
              </Stack>
            </Box>
          )}
      </Flex>
    </Box>
  );
};

export default EmployeePage;
