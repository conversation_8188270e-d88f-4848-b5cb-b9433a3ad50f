# import items by purchase
mutation importItem($args: ImportInput!) {
  importItem(args: $args) {
    ...BooleanResponse
  }
}

# add item
mutation addItem($args: ItemInput!) {
  addItem(args: $args) {
    ...BooleanResponse
  }
}

# add service
mutation addService($args: ServiceInput!) {
  addService(args: $args) {
    ...BooleanResponse
  }
}

# write off item in inventory
mutation writeOffItems($args: [WriteOffInput!]!) {
  writeOffItems(args: $args) {
    ...BooleanResponse
  }
}

# dispatch items
mutation dispatchItems($args: [DispatchInput!]!) {
  dispatchItems(args: $args) {
    ...BooleanResponse
  }
}

# instant transfer items
mutation instantTransfer(
  $args: [TransferInput!]!
  $sourceStore: Float!
  $destinationStore: Float!
) {
  instantTransfer(
    args: $args
    sourceStore: $sourceStore
    destinationStore: $destinationStore
  ) {
    ...BooleanResponse
  }
}

# sale items
mutation quickSale($args: [SaleInput!]!) {
  quickSale(args: $args) {
    ...BooleanResponse
  }
}

# serve item that was ordered on a previous order
mutation servePayLater(
  $args: [SaleInput!]!
  $servedTo: Float
  $customerTag: String
) {
  servePayLater(args: $args, servedTo: $servedTo, customerTag: $customerTag) {
    ...BooleanResponse
  }
}

# update bill
mutation updateBill($inventoryId: Float!, $args: [SaleInput!]!) {
  updateBill(inventoryId: $inventoryId, args: $args) {
    ...BooleanResponse
  }
}

# edit item that was ordered on a previous order
mutation editBillItem(
  $inventoryId: Float!
  $transferId: Float!
  $newQuantity: Float!
) {
  editBillItem(
    inventoryId: $inventoryId
    transferId: $transferId
    newQuantity: $newQuantity
  ) {
    status
    error {
      target
      message
    }
    transfer {
      id
      itemId
      inventoryId
      quantity
      details
      price
      dispatched
    }
  }
}

# delete item that was ordered on a previous order
mutation deleteBillItem($inventoryId: Float!, $transferId: Float!) {
  deleteBillItem(inventoryId: $inventoryId, transferId: $transferId) {
    status
    error {
      target
      message
    }
  }
}

# sale cashier approval
mutation clearBill($saleId: Float!) {
  clearBill(saleId: $saleId) {
    ...BooleanResponse
  }
}

# serve item that was ordered on a previous order
mutation servePendingOrder($transferId: Float!) {
  servePendingOrder(transferId: $transferId) {
    ...BooleanResponse
  }
}

# transfer items
mutation transferItems($args: [DispatchInput!]!) {
  transferItems(args: $args) {
    ...BooleanResponse
  }
}

# add items
mutation addItemsFromExcel($args: BulkItemInput!) {
  addItemsFromExcel(args: $args) {
    ...BooleanResponse
  }
}

# edit item
mutation editItem($args: ItemInput!, $id: Float!) {
  editItem(id: $id, args: $args) {
    ...BooleanResponse
  }
}

# edit service
mutation editService($args: ServiceInput!, $id: Float!) {
  editService(id: $id, args: $args) {
    ...BooleanResponse
  }
}

#delete item
mutation deleteItem($id: Float!) {
  deleteItem(id: $id) {
    ...BooleanResponse
  }
}

# approve transfer
mutation changeInventoryApprovalStatus($inventoryId: Float!) {
  changeInventoryApprovalStatus(inventoryId: $inventoryId) {
    ...BooleanResponse
  }
}

# clear served bill
mutation clearServedBill($inventoryId: Float!) {
  clearServedBill(inventoryId: $inventoryId) {
    ...BooleanResponse
  }
}

# dispatch transfer
mutation changeInventoryDispatchedStatus($inventoryId: Float!) {
  changeInventoryDispatchedStatus(inventoryId: $inventoryId) {
    ...BooleanResponse
  }
}

# sale items
mutation changeInventorySoldStatus($inventoryId: Float!) {
  changeInventorySoldStatus(inventoryId: $inventoryId) {
    ...BooleanResponse
  }
}

# receive transfer
mutation changeInventoryReceivedStatus($inventoryId: Float!) {
  changeInventoryReceivedStatus(inventoryId: $inventoryId) {
    ...BooleanResponse
  }
}

# assign store keeper
mutation assignStoreKeeper($storeId: Float!, $userId: Float!) {
  assignStoreKeeper(storeId: $storeId, userId: $userId) {
    ...BooleanResponse
  }
}

# manage units
mutation addUnit($args: UnitInput!) {
  addUnit(args: $args) {
    ...BooleanResponse
  }
}

mutation updateUnit($id: Float!, $args: UnitInput!) {
  updateUnit(id: $id, args: $args) {
    ...BooleanResponse
  }
}

mutation deleteUnit($id: Float!) {
  deleteUnit(id: $id) {
    ...BooleanResponse
  }
}
