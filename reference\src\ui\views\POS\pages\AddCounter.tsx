/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useState } from "react";
import { useForm } from "react-hook-form";
// Chakra imports
import {
  Flex,
  Button,
  FormControl,
  FormLabel,
  Input,
  Text,
  useColorModeValue,
  FormErrorMessage,
  useToast,
  Checkbox,
} from "@chakra-ui/react";
import { useAddStoreMutation } from "../../../generated/graphql";
import { useHistory } from "react-router";

type IAddCounterProps = object;

const AddCounter: React.FC<IAddCounterProps> = () => {
  const toast = useToast({
    position: "top",
  });
  const [, addStore] = useAddStoreMutation();
  const [error, seterror] = useState("");
  const history = useHistory();

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors, isSubmitting },
  } = useForm();

  async function onSubmit(values: any) {
    seterror("");
    const args = {
      name: values.name,
      primary: values.primary,
      address: values.address,
    };
    const { data } = await addStore({ args: args });
    if (data?.addStore.error) {
      return seterror(data?.addStore.error.message);
    } else {
      reset();
      history.push("counters");
      return toast({
        title: "Counter added successfully!",
        variant: "left-accent",
        status: "success",
        isClosable: true,
      });
    }
  }
  // Chakra color mode
  const textColor = useColorModeValue("gray.700", "white");
  const bgForm = useColorModeValue("white", "navy.800");

  return (
    <Flex position="relative" mb="90px">
      <Flex
        h={{ sm: "initial", md: "75vh", lg: "85vh" }}
        w="100%"
        maxW="1044px"
        mx="auto"
        justifyContent="space-between"
        mb="60px"
        pt={{ md: "0px" }}
      >
        <Flex
          w="100%"
          h="100%"
          alignItems="center"
          justifyContent="center"
          mb="60px"
          mt={{ base: "50px", md: "50px" }}
        >
          <Flex
            zIndex="2"
            direction="column"
            w="690px"
            background="transparent"
            borderRadius="15px"
            p="40px"
            mx={{ base: "100px" }}
            m={{ base: "20px", md: "auto" }}
            bg={bgForm}
            boxShadow={useColorModeValue(
              "0px 5px 14px rgba(0, 0, 0, 0.05)",
              "unset"
            )}
          >
            <Text
              fontSize="xl"
              color={textColor}
              fontWeight="bold"
              textAlign="center"
              mb="22px"
            >
              Add Counter
            </Text>
            <form onSubmit={handleSubmit(onSubmit)}>
              <FormControl isInvalid={!!errors.name} mb="24px">
                <FormLabel
                  color={textColor}
                  ms="4px"
                  fontSize="sm"
                  fontWeight="normal"
                >
                  Counter Name
                </FormLabel>
                <Input
                  type="text"
                  placeholder="Enter Counter Name"
                  color={textColor}
                  bg="gray.50"
                  borderRadius="15px"
                  fontSize="sm"
                  size="lg"
                  {...register("name", {
                    required: "Counter Name is required",
                  })}
                />
                <FormErrorMessage>
                  {errors.name && (errors.name.message as string)}
                </FormErrorMessage>
              </FormControl>
              <FormControl isInvalid={!!errors.address} mb="24px">
                <FormLabel
                  color={textColor}
                  ms="4px"
                  fontSize="sm"
                  fontWeight="normal"
                >
                  Counter Location
                </FormLabel>
                <Input
                  type="text"
                  placeholder="Enter Counter Location"
                  color={textColor}
                  bg="gray.50"
                  borderRadius="15px"
                  fontSize="sm"
                  size="lg"
                  {...register("address", {
                    required: "Counter Location is required",
                  })}
                />
                <FormErrorMessage>
                  {errors.address && (errors.address.message as string)}
                </FormErrorMessage>
              </FormControl>
              <FormControl isInvalid={!!errors.primary} mb="24px">
                <Checkbox color={textColor} {...register("primary")}>
                  Is Primary?
                </Checkbox>
                <FormErrorMessage>
                  {errors.primary && (errors.primary.message as string)}
                </FormErrorMessage>
              </FormControl>
              <FormControl>
                {error ? (
                  <Text
                    color="red"
                    fontSize="14px"
                    mb="20px"
                    textAlign="center"
                    fontWeight="bold"
                  >
                    {error}
                  </Text>
                ) : null}
              </FormControl>
              <Button
                fontSize="16px"
                type="submit"
                bg="teal.300"
                w="100%"
                h="45"
                mb="20px"
                color="white"
                mt="20px"
                _hover={{
                  bg: "teal.200",
                }}
                _active={{
                  bg: "teal.400",
                }}
                isLoading={isSubmitting}
              >
                Submit
              </Button>
            </form>
          </Flex>
        </Flex>
      </Flex>
    </Flex>
  );
};

export default AddCounter;
