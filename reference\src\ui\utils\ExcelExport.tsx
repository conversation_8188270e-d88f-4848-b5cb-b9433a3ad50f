/* eslint-disable @typescript-eslint/no-explicit-any */
import React from "react";
import {
  Button,
  Flex,
  Icon,
  Text,
  useColorModeValue,
  useToast,
} from "@chakra-ui/react";
import { saveAs } from "file-saver";
import { PiMicrosoftExcelLogoFill } from "react-icons/pi";
import * as XLSX from "xlsx";

interface Props {
  data: any[] | undefined; // Define the type of data prop
  fileName: string; // Define the type of fileName prop
}

const ExcelExport: React.FC<Props> = ({ data, fileName }) => {
  const textColor = useColorModeValue("gray.700", "white");
  const toast = useToast({
    position: "top",
  });

  const exportToExcel = () => {
    if (data) {
      const worksheet = XLSX.utils.json_to_sheet(data);
      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, "Sheet1");
      const excelBuffer = XLSX.write(workbook, {
        bookType: "xlsx",
        type: "array",
      });
      const blob = new Blob([excelBuffer], {
        type: "application/octet-stream",
      });
      saveAs(blob, `${fileName}.xlsx`);
    } else {
      toast({
        title: "Can not export items at this moment!",
        variant: "left-accent",
        status: "warning",
        isClosable: true,
      });
    }
  };

  return (
    <Button
      onClick={exportToExcel}
      px={1}
      mr={3}
      bg="transparent"
      variant="no-effects"
    >
      <Flex
        align="center"
        w={{ lg: "135px" }}
        borderRadius="15px"
        justifyContent="center"
        py="10px"
        cursor="pointer"
      >
        <Icon
          as={PiMicrosoftExcelLogoFill}
          color={textColor}
          fontSize="s"
          mr="4px"
        />
        <Text fontSize="xs" color={textColor} fontWeight="bold">
          EXPORT TO EXCEL
        </Text>
      </Flex>
    </Button>
  );
};

export default ExcelExport;
