import { StyleFunctionProps } from "@chakra-ui/theme-tools";
import { ComponentStyleConfig } from "@chakra-ui/react";

// Define Card component styles
const Card: ComponentStyleConfig = {
  baseStyle: {
    p: "22px",
    display: "flex",
    flexDirection: "column",
    width: "100%",
    boxShadow: "0px 5px 14px rgba(0, 0, 0, 0.05)",
    borderRadius: "20px",
    position: "relative",
    wordWrap: "break-word",
    backgroundClip: "border-box",
  },
  variants: {
    panel: (props: StyleFunctionProps) => ({
      bg: props.colorMode === "dark" ? "#111C44" : "white", // Set background based on colorMode
    }),
  },
  defaultProps: {
    variant: "panel", // Default variant to "panel"
  },
};

// Export Card component as part of the Chakra UI theme
export const CardComponent = {
  components: {
    Card,
  },
};
