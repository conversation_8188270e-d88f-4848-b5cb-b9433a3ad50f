import React, { useContext } from "react";
import {
  Box,
  Flex,
  Button,
  Spacer,
  IconButton,
  useDisclosure,
  AlertDialog,
  AlertDialogOverlay,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogBody,
  AlertDialogFooter,
  Text,
  useColorMode,
} from "@chakra-ui/react";
import { HamburgerIcon } from "@chakra-ui/icons";
import { IoMdLogOut, IoMdPower } from "react-icons/io";
import {
  FaBox,
  FaCashRegister,
  FaClipboardList,
  FaHourglassHalf,
} from "react-icons/fa";
import { useLogoutMutation } from "../../generated/graphql";
import { Link, useHistory } from "react-router-dom";
import { PersonIcon } from "../Icons/Icons";
import { BsFillLayersFill } from "react-icons/bs";
import { MeContext } from "../Wrapper"; // Import MeContext

interface TopNavBarProps {
  onToggleSidebar: () => void;
  height: string;
}

const TopDesktopNavBar: React.FC<TopNavBarProps> = ({
  onToggleSidebar,
  height,
}) => {
  const { isOpen, onOpen, onClose } = useDisclosure();
  const cancelRef = React.useRef<HTMLButtonElement>(null);
  const history = useHistory();
  const me = useContext(MeContext); // Get user data from MeContext

  const { colorMode } = useColorMode();
  const [, logout] = useLogoutMutation();

  const handleLogout = async () => {
    try {
      await logout({});
      console.log("Logged out successfully");
      // history.push("/login"); // Redirect to login page after logout
    } catch (error) {
      console.error("Error during logout:", error);
    }
  };

  const handleQuit = () => {
    if (window.electron && typeof window.electron.send === "function") {
      window.electron.send("app-quit", null);
    } else {
      console.error("Electron API is not available.");
    }
  };

  // Permission check function
  const hasPermission = (permission: string) => {
    return (
      me?.permissions?.some((perm) => perm.name === permission) || false
      // me?.permissions?.some((perm) => perm.name === "Administrator")    // remove false and uncomment this line to allow admin to see all routes
    );
  };

  return (
    <Box
      as="nav"
      bg={colorMode === "light" ? "teal.500" : "gray.800"}
      color={colorMode === "light" ? "white" : "gray.200"}
      px={4}
      py={2}
      boxShadow="md"
      position="sticky"
      top="0"
      zIndex="1000"
      height={height}
    >
      <Flex align="center">
        {/* Sidebar toggle button */}
        <IconButton
          icon={<HamburgerIcon />}
          aria-label="Toggle Sidebar"
          onClick={onToggleSidebar}
          colorScheme="teal"
          size="sm"
          mx={2}
        />

        {/* Navbar text */}
        <Text
          fontSize="lg"
          fontWeight="bold"
          mx={4}
          textTransform="uppercase"
          letterSpacing="widest"
          whiteSpace="nowrap"
          color={colorMode === "light" ? "gray.50" : "teal.300"}
        >
          Talisia POS
        </Text>

        {/* Middle navigation buttons with permission checks */}
        <Flex flex="1" justify="center" align="center">
          {hasPermission("POS") && (
            <Button
              leftIcon={<FaCashRegister />}
              colorScheme="blue"
              size="sm"
              mx={1}
              as={Link}
              to="/desktop/POS"
            >
              POS
            </Button>
          )}
          {hasPermission("Products") && (
            <Button
              leftIcon={<FaBox />}
              colorScheme="green"
              size="sm"
              mx={1}
              as={Link}
              to="/desktop/products"
            >
              Products
            </Button>
          )}
          {hasPermission("Open Tabs") && (
            <Button
              leftIcon={<BsFillLayersFill />}
              colorScheme="cyan"
              size="sm"
              mx={1}
              onClick={() => history.push("/desktop/open-tabs")}
            >
              Open Tabs
            </Button>
          )}
          {hasPermission("Pending Orders") && (
            <Button
              leftIcon={<FaHourglassHalf />}
              colorScheme="blue"
              size="sm"
              mx={1}
              onClick={() => history.push("/desktop/pending-orders")}
            >
              Pending Orders
            </Button>
          )}
          {hasPermission("Sales") && (
            <Button
              leftIcon={<FaClipboardList />}
              colorScheme="pink"
              size="sm"
              mx={1}
              onClick={() => history.push("/desktop/sales")}
            >
              Sales
            </Button>
          )}
          {hasPermission("Users") && (
            <Button
              leftIcon={<PersonIcon />}
              colorScheme="purple"
              size="sm"
              mx={1}
              onClick={() => history.push("/desktop/employees")}
            >
              Users
            </Button>
          )}
        </Flex>

        <Spacer />

        {/* Logout button - wider with text */}
        <Button
          leftIcon={<IoMdLogOut size={20} />}
          colorScheme="orange"
          size="sm"
          mx={1}
          w="100px" // Wider button
          borderRadius="md" // Same border radius
          onClick={handleLogout}
          aria-label="Logout User"
        >
          Logout
        </Button>

        {/* Quit button */}
        <IconButton
          aria-label="Quit App"
          colorScheme="red"
          size="sm"
          onClick={onOpen}
        >
          <IoMdPower size={30} />
        </IconButton>
      </Flex>

      {/* Quit Confirmation Dialog */}
      <AlertDialog
        isOpen={isOpen}
        leastDestructiveRef={cancelRef}
        onClose={onClose}
      >
        <AlertDialogOverlay>
          <AlertDialogContent
            borderRadius="lg"
            boxShadow="lg"
            bg={colorMode === "light" ? "gray.50" : "gray.700"}
            border="1px solid"
            borderColor={colorMode === "light" ? "gray.200" : "gray.600"}
          >
            <AlertDialogHeader
              fontSize="2xl"
              fontWeight="bold"
              color={colorMode === "light" ? "red.500" : "red.300"}
              textAlign="center"
              p={6}
            >
              Exit Talisia POS
            </AlertDialogHeader>
            <AlertDialogBody
              fontSize="md"
              textAlign="center"
              color={colorMode === "light" ? "gray.700" : "gray.300"}
              p={6}
            >
              Are you sure you want to exit the application?
            </AlertDialogBody>
            <AlertDialogFooter
              justifyContent="space-between"
              p={6}
              bg={colorMode === "light" ? "gray.100" : "gray.600"}
              borderTop="1px solid"
              borderColor={colorMode === "light" ? "gray.200" : "gray.500"}
            >
              <Button
                ref={cancelRef}
                onClick={onClose}
                colorScheme="gray"
                size="md"
              >
                Stay
              </Button>
              <Button
                colorScheme="red"
                size="md"
                onClick={() => {
                  handleQuit();
                  onClose();
                }}
              >
                Bye
              </Button>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialogOverlay>
      </AlertDialog>
    </Box>
  );
};

export default TopDesktopNavBar;
