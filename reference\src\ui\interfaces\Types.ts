export type IAction = {
  type: ActionType;
  pendingFile?: IUploadingUrl;
  cartItem?: ICartItem;
  cart_inventory?: number;
  cart?: ICart;
};

export type ICartItem = {
  id: number;
  name: string;
  unit: string;
  price: number;
  quantity: number;
  stock: number;
  hold: boolean;
};

export type ICart = {
  items: ICartItem[];
  totalAmount: number;
  itemCount: number;
};

export type IUploadingUrl = {
  uploadingFileUrl: string;
  path: string;
};

export enum ActionType {
  SET_UPLOADING_FILE_URL = "SET_UPLOADING_FILE_URL",
  CLEAR_UPLOADING_FILE_URL = "CLEAR_UPLOADING_FILE_URL",
  ADD_ITEM_TO_CART = "ADD_ITEM_TO_CART",
  REMOVE_ITEM_FROM_CART = "REMOVE_ITEM_FROM_CART",
  UPDATE_CART_ITEM_QUANTITY = "UPDATE_CART_ITEM_QUANTITY",
  CLEAR_CART = "CLEAR_CART",
  UPDATE_CART_ITEM_HOLD = "UPDATE_CART_ITEM_HOLD",
  UPDATE_SAVED_CART_ITEMS = "UPDATE_SAVED_CART_ITEMS",
}
