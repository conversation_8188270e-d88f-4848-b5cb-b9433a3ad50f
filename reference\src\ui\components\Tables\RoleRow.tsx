/* eslint-disable @typescript-eslint/no-explicit-any */
import {
  Box,
  Button,
  Flex,
  Icon,
  Text,
  useColorModeValue,
} from "@chakra-ui/react";
import React, { useContext } from "react";
import { FaPencilAlt, FaTrashAlt } from "react-icons/fa";
import { hasPermission } from "../../interfaces/Helpers";
import { MeContext } from "../Wrapper";

interface IRoleRowProps {
  name: string;
  key: number;
  editActivated: (name: string) => void;
  callOnClick: () => void;
}

const RoleRow: React.FC<IRoleRowProps> = (props: any) => {
  const textColor = useColorModeValue("gray.700", "white");
  const bgColor = useColorModeValue("#F8F9FA", "navy.900");
  const nameColor = useColorModeValue("gray.500", "white");
  const { name, editActivated, callOnClick } = props;

  const me = useContext(MeContext);
  return (
    <Box
      py={{ base: "8px", md: "10px" }} // Adjust padding for small screens
      px={{ base: "10px", md: "20px" }} // Adjust padding for small screens
      bg={bgColor}
      my={{ base: "5px", md: "10px" }} // Adjust margin for small screens
      borderRadius="12px"
      mr={5}
      w="100%"
    >
      <Flex
        justify="space-between"
        w="100%"
        direction={{ base: "column", md: "row" }}
      >
        <Flex maxWidth="70%" mb={{ base: "10px", md: "0" }}>
          <Text
            color={nameColor}
            fontSize={{ base: "lg", md: "xl" }} // Adjust font size for small screens
            mt={7}
            fontWeight="bold"
            noOfLines={1} // Prevent text from overflowing in small screens
          >
            {name}
          </Text>
        </Flex>

        <Flex
          direction={{ base: "column", md: "row" }} // Stack buttons on mobile, align them horizontally on larger screens
          align="flex-start"
          p={{ md: "24px" }}
        >
          {hasPermission(me?.permissions, [
            "Designations>Edit",
            "Designations>Delete",
          ]) && (
            <Button
              p="0px"
              bg="transparent"
              variant="no-effects"
              mb={{ base: "10px", md: "0px" }} // Add margin for mobile devices
              me={{ md: "12px" }}
              onClick={callOnClick}
            >
              <Flex color="red.200" cursor="pointer" align="center" p="12px">
                <Icon as={FaTrashAlt} me="4px" />
                <Text fontSize={{ base: "sm", md: "sm" }} fontWeight="semibold">
                  DELETE
                </Text>
              </Flex>
            </Button>
          )}
          {hasPermission(me?.permissions, [
            "Designations>Edit",
            "Designations>Delete",
          ]) && (
            <Button
              p="0px"
              bg="transparent"
              variant="no-effects"
              onClick={() => editActivated(name)}
            >
              <Flex color={textColor} cursor="pointer" align="center" p="12px">
                <Icon as={FaPencilAlt} me="4px" />
                <Text fontSize={{ base: "sm", md: "sm" }} fontWeight="semibold">
                  EDIT
                </Text>
              </Flex>
            </Button>
          )}
        </Flex>
      </Flex>
    </Box>
  );
};

export default RoleRow;
