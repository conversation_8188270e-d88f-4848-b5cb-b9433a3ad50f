/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useState, useEffect, useRef, useContext } from "react";
import {
  Box,
  Button,
  Flex,
  Input,
  Table,
  Tbody,
  Td,
  Th,
  Thead,
  Tr,
  Text,
  Image,
  Tag,
  useColorModeValue,
  Spinner,
  Center,
} from "@chakra-ui/react";
import { useTable, useSortBy, usePagination } from "react-table";
import InfiniteScroll from "react-infinite-scroll-component";
import { useGetMerchandiseItemsQuery } from "../../../generated/graphql";
import { useHistory } from "react-router-dom";
import JsBarcode from "jsbarcode";
import { formatToMoney } from "../../../utils/Helpers";
import { MeContext } from "../../../components/Wrapper";
import { hasPermission } from "../../../interfaces/Helpers";

const ProductsPage: React.FC = () => {
  const textColor = useColorModeValue("gray.700", "white");
  const history = useHistory();
  const me = useContext(MeContext);

  const [{ data, fetching }] = useGetMerchandiseItemsQuery({
    requestPolicy: "network-only",
  });
  const [searchTerm, setSearchTerm] = useState("");
  const [items, setItems] = useState(data?.getMerchandiseItems || []);

  useEffect(() => {
    if (data?.getMerchandiseItems) {
      setItems(data.getMerchandiseItems);
    }
  }, [data]);

  const columns = React.useMemo(
    () => [
      {
        Header: "Image",
        accessor: "image",
        Cell: ({ value }: { value: string }) => (
          <Image src={value} alt="Product" boxSize="50px" objectFit="cover" />
        ),
      },
      { Header: "Name", accessor: "name" },
      {
        Header: "Barcode",
        accessor: "id", // Placeholder to align with your data
        Cell: ({ row }: { row: any }) => {
          const barcodeRef = useRef<HTMLCanvasElement>(null);

          useEffect(() => {
            if (barcodeRef.current) {
              JsBarcode(barcodeRef.current, `${row.original.name}`, {
                format: "CODE128",
                lineColor: "#000",
                width: 1,
                height: 30,
                displayValue: true,
              });
            }
          }, [row.original.name]);

          return (
            <Box display={["block", "block", "block"]}>
              <canvas ref={barcodeRef}></canvas>
            </Box>
          );
        },
      },
      {
        Header: "Price",
        accessor: "sellingPrice",
        Cell: ({ row }: { row: any }) =>
          `${formatToMoney(Number(row.original.sellingPrice))}`,
      },
      { Header: "Unit", accessor: "unit" },
      {
        Header: "Stock",
        accessor: "stock",
        Cell: ({ row }: { row: any }) => {
          const stock = row.original.stock;
          const reorder = row.original.reorder;
          const unit = row.original.unit;
          if (stock === 0) {
            return <Tag colorScheme="red">{`${stock} ${unit}`}</Tag>;
          } else if (stock <= reorder) {
            return <Tag colorScheme="yellow">{`${stock} ${unit}`}</Tag>;
          } else {
            return <Tag colorScheme="green">{`${stock} ${unit}`}</Tag>;
          }
        },
      },
      {
        Header: "Actions",
        Cell: ({ row }: { row: any }) => (
          <Button
            size="sm"
            colorScheme="blue"
            onClick={() =>
              history.push({
                pathname: "product",
                state: {
                  item: row.original,
                },
              })
            }
          >
            View Details
          </Button>
        ),
      },
    ],
    [history]
  );

  const filteredItems = React.useMemo(
    () =>
      items.filter((item) =>
        item.name.toLowerCase().includes(searchTerm.toLowerCase())
      ),
    [items, searchTerm]
  );

  const tableInstance = useTable(
    {
      columns,
      data: filteredItems,
      initialState: { pageIndex: 0 },
    },
    useSortBy,
    usePagination
  );

  const { getTableProps, getTableBodyProps, headerGroups, prepareRow, rows } =
    tableInstance;

  const fetchMoreData = () => {
    if (data?.getMerchandiseItems) {
      const moreItems = data.getMerchandiseItems.slice(
        items.length,
        items.length + 10
      );
      setItems((prev) => [...prev, ...moreItems]);
    }
  };

  if (fetching) {
    return (
      <Center h="100vh">
        <Spinner size="xl" />
      </Center>
    );
  }

  return (
    <Box p={5}>
      <Flex justify="space-between" align="center" mb={4}>
        <Text fontSize="2xl" fontWeight="bold" color={textColor}>
          Products
        </Text>
        <Flex justify={"space-between"} align={"center"}>
          {hasPermission(me?.permissions, ["Import"]) && (
            <Button
              colorScheme="green"
              onClick={() => history.push("import-product")}
              ml={4}
            >
              Import Stock
            </Button>
          )}
          {hasPermission(me?.permissions, [
            "Products>Add",
            "Products>Edit",
          ]) && (
            <Button
              colorScheme="teal"
              onClick={() => history.push("manage-product")}
              ml={4}
            >
              Add Product
            </Button>
          )}
        </Flex>
      </Flex>

      <Input
        placeholder="Search by product name"
        mb={4}
        value={searchTerm}
        onChange={(e) => setSearchTerm(e.target.value)}
      />

      <InfiniteScroll
        dataLength={filteredItems.length}
        height={"65vh"}
        next={fetchMoreData}
        hasMore={
          filteredItems.length < (data?.getMerchandiseItems?.length || 0)
        }
        loader={<Text>Loading...</Text>}
        endMessage={
          <Text size={"md"} pt={5} margin={"auto"}>
            End of products list.
          </Text>
        }
      >
        <Table {...getTableProps()}>
          <Thead>
            {headerGroups.map((headerGroup) => (
              <Tr {...headerGroup.getHeaderGroupProps()}>
                {headerGroup.headers.map((column) => (
                  <Th {...column.getHeaderProps(column.getSortByToggleProps())}>
                    {column.render("Header")}
                  </Th>
                ))}
              </Tr>
            ))}
          </Thead>
          <Tbody {...getTableBodyProps()}>
            {rows.map((row) => {
              prepareRow(row);
              return (
                <Tr {...row.getRowProps()}>
                  {row.cells.map((cell) => (
                    <Td {...cell.getCellProps()}>{cell.render("Cell")}</Td>
                  ))}
                </Tr>
              );
            })}
          </Tbody>
        </Table>
      </InfiniteScroll>

      {/* If no filtered items, display a message below the table */}
      {!filteredItems.length && (
        <Box textAlign="center" mt={4}>
          <Text fontSize="xl" mb={4}>
            No products match your search
          </Text>
          <Button
            colorScheme="teal"
            onClick={() => history.push("add_edit_product")}
          >
            Add Product
          </Button>
        </Box>
      )}
    </Box>
  );
};

export default ProductsPage;
