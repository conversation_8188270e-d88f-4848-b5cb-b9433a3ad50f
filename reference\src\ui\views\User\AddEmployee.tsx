/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/no-empty-object-type */
import React, { useContext, useEffect, useState } from "react";
import { useForm } from "react-hook-form";
// Chakra imports
import {
  Flex,
  Button,
  FormControl,
  FormLabel,
  Input,
  Text,
  useColorModeValue,
  FormErrorMessage,
  useToast,
  Grid,
  GridItem,
} from "@chakra-ui/react";
import {
  useRegisterEmployeeMutation,
  useGetRolesQuery,
  useGetDepartmentsQuery,
  useGetStoresQuery,
} from "../../generated/graphql";
import { useHistory } from "react-router-dom";
import { MeContext } from "../../components/Wrapper";
import { AsyncSelect, SingleValue, ActionMeta } from "chakra-react-select";
import { HSeparator } from "../POS/components/Separator";

interface IAddEmployeeProps {}

const AddEmployee: React.FC<IAddEmployeeProps> = () => {
  const me = useContext(MeContext);
  const toast = useToast({ position: "top" });
  const [, addEmployee] = useRegisterEmployeeMutation();

  const [{ data: roles, fetching: loadingRoles }] = useGetRolesQuery();
  const [{ data: departments, fetching: loadingDepartments }] =
    useGetDepartmentsQuery();
  const [{ data: stores, fetching: loadingStores }] = useGetStoresQuery();
  const [error, seterror] = useState("");
  const history = useHistory();

  // State for storing options
  const [rolesOptions, setRolesOptions] = useState<
    { label: string; value: number; isDisabled?: boolean }[]
  >([]);
  const [departmentOptions, setDepartmentOptions] = useState<
    { label: string; value: number }[]
  >([]);
  const [storeOptions, setStoreOptions] = useState<
    { label: string; value: number }[]
  >([]);

  interface IFormInput {
    firstname: string;
    middlename: string;
    lastname: string;
    email: string;
    phone: string;
    password: string;
    role: number;
    company: number;
    companyRole: number;
    designation: string;
    licenseNumber: string;
    department: number;
    store: number;
  }

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors, isSubmitting },
    setValue,
  } = useForm<IFormInput>();

  async function onSubmit(values: any) {
    seterror("");

    // Validate email format
    if (!/^\w+([-+.']\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/.test(values.email)) {
      seterror("Invalid email format");
      return;
    }

    // Validate phone format
    if (!/^0(\d{3})\s?(\d{3})\s?(\d{3})$/.test(values.phone)) {
      seterror("Phone must be in format 0xxx xxx xxx");
      return;
    }

    const args = {
      firstname: values.firstname,
      middlename: values.middlename,
      lastname: values.lastname,
      email: values.email,
      phone: values.phone,
      password: values.password,
      companyId: values.company,
      companyRole: values.role,
      designation: values.designation,
      licenseNumber: values.licenseNumber,
      department: values.department,
      store: values.store,
    };
    const { data } = await addEmployee({ params: args });
    // Check if status is truthy. If not, treat it as an error.
    if (!data?.registerEmployee?.status) {
      const errorMessage =
        data?.registerEmployee?.error?.message || "An error occurred";
      toast({
        title: "Employee add failed!",
        variant: "left-accent",
        status: "error",
        isClosable: true,
      });
      return seterror(errorMessage);
    } else {
      reset();
      toast({
        title: "Employee added successfully!",
        variant: "left-accent",
        status: "success",
        isClosable: true,
      });
      if (me?.id)
        return history.push({
          pathname: "employees",
        });
    }
  }

  // Update rolesOptions when query data is ready
  useEffect(() => {
    if (roles && !loadingRoles) {
      const options = roles.getRoles
        .filter((r) => r.sys === false)
        .map((r) => ({
          label: r.name,
          value: r.id,
          isDisabled: r.name.toLowerCase() === "admin", // Disable the Admin option
        }));
      setRolesOptions(options);
    }
  }, [roles, loadingRoles]);

  // Update departmentOptions when query data is ready
  useEffect(() => {
    if (departments && !loadingDepartments) {
      const options = departments.getDepartments.map((d) => ({
        label: d.name,
        value: d.id,
      }));
      setDepartmentOptions(options);
    }
  }, [departments, loadingDepartments]);

  // Update storeOptions when query data is ready
  useEffect(() => {
    if (stores && !loadingStores) {
      const options = stores.getStores.map((s) => ({
        label: s.name,
        value: s.id,
      }));
      setStoreOptions(options);
    }
  }, [stores, loadingStores]);

  // Format phone number
  const formatPhoneNumber = (text: string) => {
    const cleaned = text.replace(/\D/g, "");
    const match = cleaned.match(/^(0\d{3})(\d{3})(\d{3})$/);
    if (match) {
      return `${match[1]} ${match[2]} ${match[3]}`;
    }
    return text;
  };

  // Helper functions to filter options based on search text
  const filterRolesOptions = async (inputValue: string) => {
    return rolesOptions.filter((option) => {
      // Keep the Admin option disabled in search results
      if (option.label.toLowerCase() === "admin") {
        option.isDisabled = true;
      }
      return option.label.toLowerCase().includes(inputValue.toLowerCase());
    });
  };

  const filterDepartmentOptions = async (inputValue: string) => {
    return departmentOptions.filter((option) =>
      option.label.toLowerCase().includes(inputValue.toLowerCase())
    );
  };

  const filterStoreOptions = async (inputValue: string) => {
    return storeOptions.filter((option) =>
      option.label.toLowerCase().includes(inputValue.toLowerCase())
    );
  };

  // Chakra color mode
  const textColor = useColorModeValue("gray.700", "white");
  const bgForm = useColorModeValue("white", "navy.800");

  // For handling changes in AsyncSelect (for department in this example)
  function handleChange(
    newValue: SingleValue<{ label: string; value: number }>,
    actionMeta: ActionMeta<{ label: string; value: number }>
  ): void {
    setValue(actionMeta.name as any, newValue!.value);
  }

  return (
    <Flex
      direction="column"
      position="relative"
      mb="20px"
      mt="10px"
      flex="1" // Take remaining vertical space
      overflowY="auto"
      maxHeight={{ base: "calc(100vh - 0px)", md: "calc(100vh - 0px)" }}
      sx={{
        "&::-webkit-scrollbar": {
          width: "1px",
        },
        "&::-webkit-scrollbar-thumb": {
          background: "gray.300",
          borderRadius: "4px",
        },
        "&::-webkit-scrollbar-thumb:hover": {
          background: "gray.500",
        },
      }}
    >
      <Flex
        h={{ sm: "initial", md: "75vh", lg: "85vh" }}
        w="100%"
        maxW="1044px"
        mx="auto"
        justifyContent="space-between"
        mb="60px"
        pt={{ md: "0px" }}
      >
        <Flex
          w="100%"
          h="100%"
          alignItems="center"
          justifyContent="center"
          mb="60px"
          mt={{ base: "50px", md: "50px" }}
        >
          <Flex
            zIndex="2"
            direction="column"
            w="690px"
            background="transparent"
            borderRadius="15px"
            p="40px"
            mx={{ base: "100px" }}
            m={{ base: "20px", md: "auto" }}
            bg={bgForm}
            boxShadow={useColorModeValue(
              "0px 5px 14px rgba(0, 0, 0, 0.05)",
              "unset"
            )}
          >
            <Text
              fontSize="xl"
              color={textColor}
              fontWeight="bold"
              textAlign="center"
              mb="12px"
            >
              Add Employee
            </Text>

            <HSeparator mt="6px" mb="6px" />
            <Text textAlign="center" mt="0px" mb="0px">
              Employee Details:
            </Text>
            <HSeparator mb="22px" mt="6px" />
            {error && (
              <Text pb="15px" m={"auto"} color="red.500" textColor="red.300">
                {error}
              </Text>
            )}
            <form onSubmit={handleSubmit(onSubmit)}>
              <Grid templateColumns="repeat(6, 1fr)" gap={6}>
                <FormControl
                  as={GridItem}
                  colSpan={3}
                  mb={5}
                  isInvalid={errors.firstname as any}
                >
                  <FormLabel htmlFor="firstname" fontSize="xs">
                    Firstname
                  </FormLabel>
                  <Input
                    variant="filled"
                    ms="4px"
                    type="text"
                    fontSize="xs"
                    placeholder="Employee Firstname"
                    mb="4px"
                    size="lg"
                    id="firstname"
                    {...register("firstname", {
                      required: "Employee firstname is required",
                      pattern: {
                        value: /^[a-zA-Z- -0-9]+$/,
                        message:
                          "Employee firstname can not have special characters",
                      },
                    })}
                  />
                  <FormErrorMessage mb="10px" fontSize="xs">
                    {errors.firstname && (errors.firstname.message as any)}
                  </FormErrorMessage>
                </FormControl>
                <FormControl
                  as={GridItem}
                  colSpan={3}
                  mb={5}
                  isInvalid={errors.middlename as any}
                >
                  <FormLabel htmlFor="middlename" fontSize="xs">
                    Middlename
                  </FormLabel>
                  <Input
                    variant="filled"
                    ms="4px"
                    type="text"
                    fontSize="xs"
                    placeholder="Employee Middlename"
                    mb="4px"
                    size="lg"
                    id="middlename"
                    {...register("middlename")}
                  />
                  <FormErrorMessage mb="10px" fontSize="xs">
                    {errors.middlename && (errors.middlename.message as any)}
                  </FormErrorMessage>
                </FormControl>
                <FormControl
                  as={GridItem}
                  colSpan={3}
                  mb={5}
                  isInvalid={errors.lastname as any}
                >
                  <FormLabel htmlFor="lastname" fontSize="xs">
                    Lastname
                  </FormLabel>
                  <Input
                    variant="filled"
                    ms="4px"
                    type="text"
                    fontSize="xs"
                    placeholder="Employee Lastname"
                    mb="4px"
                    size="lg"
                    id="lastname"
                    {...register("lastname", {
                      required: "Employee lastname is required",
                    })}
                  />
                  <FormErrorMessage mb="10px" fontSize="xs">
                    {errors.lastname && (errors.lastname.message as any)}
                  </FormErrorMessage>
                </FormControl>
                <FormControl
                  as={GridItem}
                  colSpan={3}
                  mb={5}
                  isInvalid={errors.email as any}
                >
                  <FormLabel htmlFor="email" fontSize="xs">
                    Email
                  </FormLabel>
                  <Input
                    variant="filled"
                    ms="4px"
                    type="email"
                    fontSize="xs"
                    placeholder="Employee email"
                    mb="4px"
                    size="lg"
                    id="email"
                    {...register("email", {
                      required: "Employee email is required",
                      pattern: {
                        value: /^\w+([-+.']\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/,
                        message: "Invalid email format",
                      },
                    })}
                  />
                  <FormErrorMessage mb="10px" fontSize="xs">
                    {errors.email && (errors.email.message as any)}
                  </FormErrorMessage>
                </FormControl>
                <FormControl
                  as={GridItem}
                  colSpan={3}
                  mb={5}
                  isInvalid={errors.phone as any}
                >
                  <FormLabel htmlFor="phone" fontSize="xs">
                    Phone
                  </FormLabel>
                  <Input
                    variant="filled"
                    ms="4px"
                    type="text"
                    fontSize="xs"
                    placeholder="Employee phone (0xxx xxx xxx)"
                    mb="4px"
                    size="lg"
                    maxLength={13}
                    id="phone"
                    {...register("phone", {
                      required: "Phone number is required",
                      pattern: {
                        value: /^0(\d{3})\s?(\d{3})\s?(\d{3})$/,
                        message: "Phone must be in format 0xxx xxx xxx",
                      },
                    })}
                    onChange={(e) => {
                      const formattedValue = formatPhoneNumber(e.target.value);
                      e.target.value = formattedValue;
                      setValue("phone", formattedValue);
                    }}
                  />
                  <FormErrorMessage mb="10px" fontSize="xs">
                    {errors.phone && (errors.phone.message as any)}
                  </FormErrorMessage>
                </FormControl>
                <FormControl
                  as={GridItem}
                  colSpan={3}
                  mb={5}
                  isInvalid={errors.password as any}
                >
                  <FormLabel htmlFor="password" fontSize="xs">
                    Password
                  </FormLabel>
                  <Input
                    variant="filled"
                    ms="4px"
                    type="password"
                    fontSize="xs"
                    placeholder="Password"
                    mb="4px"
                    size="lg"
                    id="password"
                    {...register("password", {
                      required: "Password is required",
                    })}
                  />
                  <FormErrorMessage mb="10px" fontSize="xs">
                    {errors.password && (errors.password.message as any)}
                  </FormErrorMessage>
                </FormControl>
                <FormControl
                  as={GridItem}
                  colSpan={3}
                  mb={5}
                  isInvalid={errors.role as any}
                >
                  <FormLabel htmlFor="role" fontSize="xs">
                    Role
                  </FormLabel>
                  <AsyncSelect
                    variant="flushed"
                    {...register("role")}
                    name="role"
                    id="role"
                    size="sm"
                    defaultOptions={rolesOptions} // full list on initial load
                    loadOptions={filterRolesOptions} // search functionality enabled
                    cacheOptions={false}
                    isLoading={loadingRoles}
                    isSearchable={true}
                    placeholder="Select a designation"
                    closeMenuOnSelect={true}
                    escapeClearsValue={true}
                    hideSelectedOptions={true}
                    isOptionDisabled={(option: any) =>
                      option.isDisabled === true
                    }
                    onChange={(event: any) => {
                      // Prevent selecting Admin role
                      if (
                        event &&
                        event.label &&
                        event.label.toLowerCase() !== "admin"
                      ) {
                        setValue("role", event.value);
                      } else if (
                        event &&
                        event.label &&
                        event.label.toLowerCase() === "admin" &&
                        me?.role!.name === "admin"
                      ) {
                        setValue("role", event.value);
                      }
                    }}
                  />
                  <FormErrorMessage mb="10px" fontSize="xs">
                    {errors.role && (errors.role.message as any)}
                  </FormErrorMessage>
                </FormControl>
                <FormControl
                  as={GridItem}
                  colSpan={3}
                  mb={5}
                  isInvalid={errors.department as any}
                >
                  <FormLabel htmlFor="department" fontSize="xs">
                    Department
                  </FormLabel>
                  <AsyncSelect
                    variant="flushed"
                    name="department"
                    id="department"
                    size="sm"
                    defaultOptions={departmentOptions} // full list on initial load
                    loadOptions={filterDepartmentOptions} // enable search filtering
                    cacheOptions={false}
                    isLoading={loadingDepartments}
                    isSearchable={true}
                    placeholder="Select Department"
                    closeMenuOnSelect={true}
                    escapeClearsValue={true}
                    hideSelectedOptions={true}
                    onChange={handleChange}
                  />
                  <FormErrorMessage mb="10px" fontSize="xs">
                    {errors.department && (errors.department.message as any)}
                  </FormErrorMessage>
                </FormControl>
                <FormControl
                  as={GridItem}
                  colSpan={3}
                  mb={5}
                  isInvalid={errors.designation as any}
                >
                  <FormLabel htmlFor="designation" fontSize="xs">
                    Designation
                  </FormLabel>
                  <Input
                    variant="filled"
                    ms="4px"
                    type="text"
                    fontSize="xs"
                    placeholder="Designation"
                    mb="4px"
                    size="lg"
                    id="designation"
                    {...register("designation", {
                      required: "Designation is required",
                    })}
                  />
                  <FormErrorMessage mb="10px" fontSize="xs">
                    {errors.designation && (errors.designation.message as any)}
                  </FormErrorMessage>
                </FormControl>{" "}
                <FormControl
                  as={GridItem}
                  colSpan={3}
                  mb={5}
                  isInvalid={errors.licenseNumber as any}
                >
                  <FormLabel htmlFor="licenseNumber" fontSize="xs">
                    License Number
                  </FormLabel>
                  <Input
                    variant="filled"
                    ms="4px"
                    type="text"
                    fontSize="xs"
                    placeholder="License Number"
                    mb="4px"
                    size="lg"
                    id="licenseNumber"
                    {...register("licenseNumber", {
                      required: "License Number is required",
                    })}
                  />
                  <FormErrorMessage mb="10px" fontSize="xs">
                    {errors.licenseNumber &&
                      (errors.licenseNumber.message as any)}
                  </FormErrorMessage>
                </FormControl>
                <FormControl
                  as={GridItem}
                  colSpan={3}
                  mb={5}
                  isInvalid={errors.store as any}
                >
                  <FormLabel htmlFor="store" fontSize="xs">
                    Store
                  </FormLabel>
                  <AsyncSelect
                    variant="flushed"
                    name="store"
                    id="store"
                    size="sm"
                    defaultOptions={storeOptions} // full list on initial load
                    loadOptions={filterStoreOptions} // enable search filtering
                    cacheOptions={false}
                    isLoading={loadingStores}
                    isSearchable={true}
                    placeholder="Select Store"
                    closeMenuOnSelect={true}
                    escapeClearsValue={true}
                    hideSelectedOptions={true}
                    onChange={(event: any) => {
                      setValue("store", event.value);
                    }}
                  />
                  <FormErrorMessage mb="10px" fontSize="xs">
                    {errors.store && (errors.store.message as any)}
                  </FormErrorMessage>
                </FormControl>
              </Grid>
              <Button
                type="submit"
                variant="outline"
                fontSize="xs"
                w="100%"
                h="45"
                mb="24px"
                isLoading={isSubmitting}
              >
                Submit
              </Button>
            </form>
          </Flex>
        </Flex>
      </Flex>
    </Flex>
  );
};

export default AddEmployee;
