import React, { useState, useEffect, useContext } from "react";
import {
  Box,
  Text,
  Image,
  Button,
  VStack,
  useColorModeValue,
  Icon,
  Flex,
  Select,
  useToast, // Import useToast for notifications
} from "@chakra-ui/react";
import { FoodDrinkIcon } from "../../../components/Icons/Icons";
import { AppContext } from "../../../AppContext";

// Define the Unit type
interface Unit {
  id: number;
  name: string;
  quantity: number;
  price: number;
}

interface Product {
  id: number;
  name: string;
  image: string;
  stock: number;
  price: number;
  hold: boolean;
  unit: string;
  reference: string; // reference value for filtering by category
  units?: Unit[]; // Optional array of units
}

interface ProductListProps {
  products: Product[];
  search: string;
  category: string; // category is now used to filter by reference
  onAddToCart: (product: Product, unitName: string, unitPrice: number) => void;
}

const ProductList: React.FC<ProductListProps> = ({
  products,
  search,
  category,
  onAddToCart,
}) => {
  const [filteredProducts, setFilteredProducts] = useState<Product[]>(products);
  const [selectedUnit, setSelectedUnit] = useState<string | null>(null);
  const [selectedUnitPrice, setSelectedUnitPrice] = useState<number>(0);

  // context
  const context = useContext(AppContext);

  // Chakra UI toast for notifications
  const toast = useToast({ position: "top" });

  // Colors for the components
  const textColor = useColorModeValue("gray.800", "whiteAlpha.900");
  const buttonBg = useColorModeValue("teal.400", "teal.600");
  const buttonHoverBg = useColorModeValue("teal.500", "teal.700");
  const priceTextColor = useColorModeValue("white.500", "white.200");

  // Update filtered products whenever `products`, `search`, or `category` changes
  useEffect(() => {
    const filtered = products.filter((product) => {
      // Filter by category (reference)
      const matchesCategory = category ? product.reference === category : true;

      // Filter by search term (case-insensitive)
      const matchesSearch = search
        ? product.name.toLowerCase().includes(search.toLowerCase())
        : true;

      return matchesCategory && matchesSearch;
    });

    setFilteredProducts(filtered);
  }, [products, search, category, context.state.cart.items]);

  // Handle unit change and update the price
  const handleUnitChange = (
    productId: number,
    unitName: string,
    unitPrice: number
  ) => {
    setSelectedUnit(unitName);
    setSelectedUnitPrice(unitPrice);
    // onAddToCart(
    //   { ...products.find((prod) => prod.id === productId)! },
    //   unitName,
    //   unitPrice
    // ); // Send the updated unit details
  };

  return (
    <>
      {filteredProducts.map((product) => (
        <Box
          key={product.id}
          borderRadius="lg"
          shadow="xl"
          p={4}
          backgroundColor={"#ffff"}
          textAlign="center"
          display="flex"
          flexDirection="column"
          alignItems="center"
          gap={2}
          transition="transform 0.2s, box-shadow 0.2s"
          _hover={{
            transform: "scale(1.05)",
            boxShadow: "2xl",
          }}
        >
          {/* Product Image */}
          <Image
            src={product.image}
            alt={product.name}
            objectFit="contain"
            borderRadius="md"
            height="120px"
            bg="transparent"
            maxWidth="100%"
          />

          {/* Product Details */}
          <VStack spacing={1} mt={2}>
            <Text
              fontWeight="bold"
              fontSize="md"
              color={textColor}
              noOfLines={1}
            >
              {product.name}
            </Text>
            <Text fontSize="sm" color="gray.500">
              Stock: {product.stock}
            </Text>

            {/* Price and Unit Dropdown */}
            {product.units && product.units.length > 1 ? (
              <Select
                value={selectedUnit || product.unit}
                onChange={(e) =>
                  handleUnitChange(
                    product.id,
                    e.target.value,
                    product.units!.find((unit) => unit.name === e.target.value)
                      ?.price ?? product.price
                  )
                }
                size="sm"
                width="100%"
                isDisabled={context.state.cart.items.some(
                  (item) => item.id === product.id
                )}
              >
                <option value={product.unit}>
                  {product.unit} - {product.price} Tsh
                </option>
                {product.units.map((unit) => (
                  <option key={unit.id} value={unit.name}>
                    {unit.name} - {unit.price} Tsh
                  </option>
                ))}
              </Select>
            ) : (
              <Text fontSize="lg" color={priceTextColor}>
                {product.unit} - {product.price} Tsh
              </Text>
            )}
          </VStack>

          {/* Add to Cart Button */}
          <Button
            mt={3}
            size="md"
            bg={product.stock === 0 ? "yellow.400" : buttonBg}
            color="white"
            borderRadius="full"
            _hover={{ bg: product.stock === 0 ? "red.200" : buttonHoverBg }}
            width="100%"
            onClick={() => {
              // Check if the product is out of stock or the cart has reached the limit
              if (
                product.stock === 0
                // ||
                // (context.state.cart.items.find((item) => item.id === product.id)
                //   ?.quantity ?? 0) >= product.stock
              ) {
                // Show a toast notification if out of stock or cart limit reached
                toast({
                  title: "Out of Stock",
                  description: "Sorry, this product is currently out of stock.",
                  status: "error",
                  duration: 3000,
                  isClosable: true,
                });
              } else {
                // Proceed with adding to cart
                onAddToCart(
                  product,
                  selectedUnit || product.unit,
                  selectedUnitPrice || product.price
                );
              }
            }}
          >
            <Flex justify="space-between" align="center" width="100%">
              <Flex align="center">
                <Icon as={FoodDrinkIcon} mr={2} fontSize={30} />
                Order
              </Flex>
              <Flex color={priceTextColor}>
                {selectedUnitPrice || product.price} Tsh
              </Flex>
            </Flex>
          </Button>
        </Box>
      ))}
    </>
  );
};

export default ProductList;
