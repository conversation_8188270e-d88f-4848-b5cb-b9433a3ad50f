type LogEntry {
  timestamp: String!
  level: String!
  message: String!
  companyId: Int
  errorCode: String
  userId: Int
  stackTrace: String
  severity: String
  action: String
}

query getLogs(
  $startDate: DateTime
  $endDate: DateTime
  $level: String
  $companyId: Int
  $errorCode: String
  $userId: Int
) {
  getLogs(
    startDate: $startDate
    endDate: $endDate
    level: $level
    companyId: $companyId
    errorCode: $errorCode
    userId: $userId
  ) {
    timestamp
    level
    message
    companyId
    errorCode
    userId
    stackTrace
    severity
    action
  }
}

query getTodaysErrors {
  getTodaysErrors {
    timestamp
    level
    message
    companyId
    errorCode
    userId
    stackTrace
    severity
    action
  }
}

query getCompanyErrors(
  $companyId: Int!
  $days: Int = 7
  $startDate: DateTime
  $endDate: DateTime
  $severity: String
) {
  getCompanyErrors(
    companyId: $companyId
    days: $days
    startDate: $startDate
    endDate: $endDate
    severity: $severity
  ) {
    timestamp
    level
    message
    companyId
    errorCode
    userId
    stackTrace
    severity
    action
  }
}
