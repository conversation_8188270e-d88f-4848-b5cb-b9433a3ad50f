/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useContext, useState } from "react";
import {
  Container,
  Heading,
  Text,
  Grid,
  GridItem,
  Box,
  FormControl,
  FormLabel,
  Select,
  Input,
  Button,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  IconButton,
  Spinner,
  Alert,
  AlertIcon,
  useToast,
} from "@chakra-ui/react";
import { motion } from "framer-motion";
import { useForm, Controller } from "react-hook-form";
import {
  useGetMerchandiseItemsQuery,
  useGetStoresQuery,
  useInstantTransferMutation,
} from "../../../generated/graphql";
import { AddIcon, DeleteIcon } from "@chakra-ui/icons";
import { BsSend } from "react-icons/bs";
import { hasPermission } from "../../../interfaces/Helpers";
import { MeContext } from "../../../components/Wrapper";

// Animation variants
const containerVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0, transition: { duration: 0.5 } },
};

interface TransferItem {
  itemId: number;
  quantity: number;
  unit: string;
  batch?: string;
}

const TransferPage: React.FC = () => {
  const { control, handleSubmit, reset } = useForm();
  const [transferItems, setTransferItems] = useState<TransferItem[]>([]);
  const [sourceStore, setSourceStore] = useState<number | null>(null);
  const [destinationStore, setDestinationStore] = useState<number | null>(null);
  const [selectedItem, setSelectedItem] = useState<string>("");

  const toast = useToast({ position: "top" });
  const me = useContext(MeContext);

  // GraphQL Queries
  const [{ data: storesData, fetching: storesFetching, error: storesError }] =
    useGetStoresQuery({ requestPolicy: "cache-and-network" });
  const [{ data: itemsData, fetching: itemsFetching, error: itemsError }] =
    useGetMerchandiseItemsQuery({ requestPolicy: "network-only" });

  // GraphQL Mutation
  const [
    { fetching: transferFetching, error: transferError },
    executeTransfer,
  ] = useInstantTransferMutation();

  // Add item to transfer list
  const addItemToTransfer = (data: any) => {
    const item = itemsData?.getMerchandiseItems.find(
      (i) => i.id === parseInt(selectedItem)
    );
    if (!item || !sourceStore) return;

    const newTransferItem: TransferItem = {
      itemId: item.id,
      quantity: parseFloat(data.quantity),
      unit: data.unit,
      batch: data.batch || undefined,
    };
    setTransferItems([...transferItems, newTransferItem]);
    reset({ quantity: "", unit: item.unit, batch: "", remarks: "" });
    setSelectedItem("");
  };

  // Remove item from transfer list
  const removeItem = (index: number) => {
    setTransferItems(transferItems.filter((_, i) => i !== index));
  };

  // Submit transfer
  const onSubmit = async () => {
    if (!sourceStore || !destinationStore || transferItems.length === 0) {
      toast({
        title: "Please select source/destination stores and add items.",
        variant: "left-accent",
        status: "warning",
        isClosable: true,
      });
      return;
    }

    const result = await executeTransfer({
      args: transferItems,
      sourceStore: sourceStore,
      destinationStore: destinationStore,
    });

    if (result.data?.instantTransfer.status) {
      toast({
        title: "Transfer completed successfully!",
        variant: "left-accent",
        status: "success",
        isClosable: true,
      });
      setTransferItems([]);
      setSourceStore(null);
      setDestinationStore(null);
    } else if (result.data?.instantTransfer.status === false)
      return toast({
        title: result.data?.instantTransfer.error?.message,
        variant: "left-accent",
        status: "warning",
        isClosable: true,
      });
    else if (result.error)
      return toast({
        title: result.error.message,
        variant: "left-accent",
        status: "warning",
        isClosable: true,
      });
  };

  // Loading and Error States
  if (storesFetching || itemsFetching) {
    return (
      <Container centerContent py={10}>
        <Spinner size="xl" />
      </Container>
    );
  }

  if (storesError || itemsError) {
    return (
      <Container py={5}>
        <Alert status="error">
          <AlertIcon />
          Error loading data: {storesError?.message || itemsError?.message}
        </Alert>
      </Container>
    );
  }

  const stores = storesData?.getStores || [];
  const items = itemsData?.getMerchandiseItems || [];

  return (
    <Container
      maxW="container.lg"
      py={2}
      flex="1" // Take remaining vertical space
      overflowY="auto"
      maxHeight={{ base: "calc(100vh - 0px)", md: "calc(100vh - 0px)" }}
      sx={{
        "&::-webkit-scrollbar": {
          width: "1px",
        },
        "&::-webkit-scrollbar-thumb": {
          background: "gray.300",
          borderRadius: "4px",
        },
        "&::-webkit-scrollbar-thumb:hover": {
          background: "gray.500",
        },
      }}
    >
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        <Heading as="h4" size="lg" textAlign="center" color="teal.600" mb={2}>
          Instant Store Transfer
        </Heading>
        <Text textAlign="center" color="gray.600" mb={6}>
          Transfer items between stores with ease
        </Text>

        <Grid templateColumns={{ base: "1fr", md: "1fr 1fr" }} gap={6}>
          {/* Store Selection */}
          {(hasPermission(me?.permissions, ["Transfer>All"]) ||
            me?.employee?.storeId) && (
            <GridItem colSpan={{ base: 1, md: 1 }}>
              <Box
                p={6}
                borderRadius="lg"
                boxShadow="md"
                bgGradient="linear(to-br, white, gray.50)"
              >
                <Heading as="h6" size="md" mb={4}>
                  Select Stores
                </Heading>
                <FormControl mb={4}>
                  <FormLabel>Source Store</FormLabel>
                  {hasPermission(me?.permissions, ["Transfer>All"]) ? (
                    <Select
                      value={sourceStore || ""}
                      onChange={(e) => setSourceStore(parseInt(e.target.value))}
                      placeholder="Select source store"
                    >
                      {stores.map((store) => (
                        <option key={store.id} value={store.id}>
                          {store.name} {store.primary && "(Primary)"}
                        </option>
                      ))}
                    </Select>
                  ) : (
                    <Text>{me?.employee?.store?.name}</Text>
                  )}
                </FormControl>
                <FormControl>
                  <FormLabel>Destination Store</FormLabel>
                  <Select
                    value={destinationStore || ""}
                    onChange={(e) =>
                      setDestinationStore(parseInt(e.target.value))
                    }
                    placeholder="Select destination store"
                    isDisabled={!sourceStore}
                  >
                    {stores
                      .filter((s) => s.id !== sourceStore)
                      .map((store) => (
                        <option key={store.id} value={store.id}>
                          {store.name} {store.primary && "(Primary)"}
                        </option>
                      ))}
                  </Select>
                </FormControl>
              </Box>
            </GridItem>
          )}

          {/* Item Selection */}
          {(hasPermission(me?.permissions, ["Transfer>All"]) ||
            me?.employee?.storeId) && (
            <GridItem colSpan={{ base: 1, md: 1 }}>
              <Box
                p={6}
                borderRadius="lg"
                boxShadow="md"
                bgGradient="linear(to-br, white, gray.50)"
              >
                <Heading as="h6" size="md" mb={4}>
                  Add Item to Transfer
                </Heading>
                <form onSubmit={handleSubmit(addItemToTransfer)}>
                  <FormControl mb={4}>
                    <FormLabel>Item</FormLabel>
                    <Select
                      value={selectedItem}
                      onChange={(e) => setSelectedItem(e.target.value)}
                      placeholder="Select an item"
                      isDisabled={!sourceStore}
                    >
                      {items.map((item) => (
                        <option key={item.id} value={item.id}>
                          {item.name} (Stock: {item.stock} {item.unit})
                        </option>
                      ))}
                    </Select>
                  </FormControl>

                  {selectedItem && (
                    <>
                      <Controller
                        name="quantity"
                        control={control}
                        defaultValue=""
                        rules={{ required: true, min: 0.1 }}
                        render={({ field, fieldState }) => (
                          <FormControl mb={4} isInvalid={!!fieldState.error}>
                            <FormLabel>Quantity</FormLabel>
                            <Input
                              {...field}
                              type="number"
                              placeholder="Enter quantity"
                              value={field.value || ""}
                            />
                            {fieldState.error && (
                              <Text color="red.500" fontSize="sm">
                                Quantity is required and must be greater than 0
                              </Text>
                            )}
                          </FormControl>
                        )}
                      />
                      <Controller
                        name="unit"
                        control={control}
                        defaultValue={
                          items.find((i) => i.id === parseInt(selectedItem))
                            ?.unit
                        }
                        render={({ field }) => (
                          <FormControl mb={4}>
                            <FormLabel>Unit</FormLabel>
                            <Select {...field} placeholder="Select unit">
                              <option
                                key={"kd1"}
                                value={
                                  items.find(
                                    (i) => i.id === parseInt(selectedItem)
                                  )?.unit
                                }
                              >
                                {
                                  items.find(
                                    (i) => i.id === parseInt(selectedItem)
                                  )?.unit
                                }
                              </option>
                              {items
                                .find((i) => i.id === parseInt(selectedItem))
                                ?.units.map((unit) => (
                                  <option key={unit.id} value={unit.name}>
                                    {unit.name} ({unit.quantity} {unit.name})
                                  </option>
                                ))}
                            </Select>
                          </FormControl>
                        )}
                      />
                      <Controller
                        name="batch"
                        control={control}
                        defaultValue=""
                        render={({ field }) => (
                          <FormControl mb={4}>
                            <FormLabel>Batch (Optional)</FormLabel>
                            <Input {...field} placeholder="Enter batch" />
                          </FormControl>
                        )}
                      />
                      <Button
                        type="submit"
                        colorScheme="teal"
                        leftIcon={<AddIcon />}
                        width="full"
                      >
                        Add to Transfer
                      </Button>
                    </>
                  )}
                </form>
              </Box>
            </GridItem>
          )}

          {/* Transfer List */}
          <GridItem colSpan={{ base: 1, md: 2 }}>
            <Box
              p={6}
              borderRadius="lg"
              boxShadow="md"
              bgGradient="linear(to-br, white, gray.50)"
            >
              <Heading as="h6" size="md" mb={4}>
                Transfer Items ({transferItems.length})
              </Heading>
              {transferItems.length === 0 ? (
                <Text color="gray.500" textAlign="center">
                  No items added for transfer.
                </Text>
              ) : (
                <Table variant="simple">
                  <Thead>
                    <Tr>
                      <Th>Item</Th>
                      <Th>Quantity</Th>
                      <Th>Unit</Th>
                      <Th>Batch</Th>
                      <Th>Actions</Th>
                    </Tr>
                  </Thead>
                  <Tbody>
                    {transferItems.map((item, index) => (
                      <Tr
                        key={index}
                        _hover={{
                          bg: "gray.100",
                          transition: "background-color 0.2s",
                        }}
                      >
                        <Td>{items.find((i) => i.id === item.itemId)?.name}</Td>
                        <Td>{item.quantity}</Td>
                        <Td>{item.unit}</Td>
                        <Td>{item.batch || "N/A"}</Td>
                        <Td>
                          <IconButton
                            aria-label="Delete"
                            icon={<DeleteIcon />}
                            colorScheme="red"
                            onClick={() => removeItem(index)}
                          />
                        </Td>
                      </Tr>
                    ))}
                  </Tbody>
                </Table>
              )}
              {transferItems.length > 0 && (
                <Box mt={4} textAlign="right">
                  <Button
                    colorScheme="blue"
                    leftIcon={<BsSend />}
                    onClick={onSubmit}
                    isLoading={transferFetching}
                  >
                    {transferFetching ? "Transferring..." : "Submit Transfer"}
                  </Button>
                </Box>
              )}
            </Box>
          </GridItem>

          {/* Transfer Error */}
          {transferError && (
            <GridItem colSpan={{ base: 1, md: 2 }}>
              <Alert status="error">
                <AlertIcon />
                Error: {transferError.message}
              </Alert>
            </GridItem>
          )}
        </Grid>
      </motion.div>
    </Container>
  );
};

export default TransferPage;
