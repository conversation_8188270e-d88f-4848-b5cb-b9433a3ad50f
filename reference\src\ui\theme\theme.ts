import { extendTheme } from "@chakra-ui/react";
import { globalStyles } from "./styles";
import { breakpoints } from "./foundations/breakpoints";
import { CardComponent } from "./additions/card/Card";
import { MainPanelComponent } from "./additions/layout/MainPanel";
import { PanelContainerComponent } from "./additions/layout/PanelContainer";
import { PanelContentComponent } from "./additions/layout/PanelContent";
import { badgeStyles } from "./components/badge";
import { buttonStyles } from "./components/button";
import { inputStyles } from "./components/input";
import { linkStyles } from "./components/link";

// Defining a type for the theme if needed (optional but helpful for type safety)
type Theme = {
  breakpoints: typeof breakpoints;
  config: {
    initialColorMode: "light" | "dark" | "system" | undefined; // Default color mode
    useSystemColorMode: boolean; // Use user preferences (e.g., OS light/dark mode)
  };
};

export default extendTheme(
  {
    breakpoints, // Breakpoints
    config: {
      initialColorMode: "light", // Default color mode
      useSystemColorMode: false, // Use user preferences (e.g., OS light/dark mode)
    },
  },
  globalStyles,
  buttonStyles, // Button styles
  badgeStyles, // Badge styles
  linkStyles, // Link styles
  inputStyles, // Input styles
  CardComponent, // Card component
  MainPanelComponent, // Main Panel component
  PanelContentComponent, // Panel Content component
  PanelContainerComponent // Panel Container component
) as Theme;
