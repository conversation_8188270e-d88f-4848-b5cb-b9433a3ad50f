/* eslint-disable @typescript-eslint/no-unused-vars */
import { cacheExchange, Cache } from "@urql/exchange-graphcache";
import { createClient, fetchExchange } from "urql";
import {
  LoginMutation,
  MeQuery,
  MeDocument,
  LogoutMutation,
  ResetPasswordMutation,
} from "../generated/graphql";
import betterUpdateQuery from "./BetterUpdateQuery";

export async function getApiUrl() {
  try {
    const result = await window.electron.invoke("get-deployment-type", {});

    // If deployment type is online, use the online URL
    if (result.isServer === null || result.isServer === undefined) {
      return "https://talisiapos.duckdns.org/graphql";
    }

    // If deployment type is LAN (isServer is false and not online), use saved server URL or localhost
    if (result.isServer === false) {
      // Try to get saved server URL
      const serverUrlResult = await window.electron.invoke(
        "get-server-url",
        {}
      );
      if (serverUrlResult.serverUrl) {
        return serverUrlResult.serverUrl;
      }
      // Fall back to online URL if no saved server URL
      return "https://talisiapos.duckdns.org/graphql";
    }

    // Otherwise use the environment variable or fallback to server URL
    if (result.isServer === true)
      return import.meta.env.VITE_API_URL || "https://localhost:5823/graphql";
    else return "https://talisiapos.duckdns.org/graphql";
  } catch (error) {
    console.error("Failed to get deployment type:", error);
    // Default to online URL if Electron call fails
    return "https://talisiapos.duckdns.org/graphql";
  }
}

// Since getApiUrl is now async, we need to handle this differently
// We'll initialize with a default URL and update it when we can
let apiUrl: string = "https://talisiapos.duckdns.org/graphql";

// Try to get the actual URL asynchronously
getApiUrl()
  .then((url) => {
    apiUrl = url;
  })
  .catch((err) => {
    console.error("Error getting API URL:", err);
  });

const client = createClient({
  url: apiUrl,
  fetchOptions: {
    credentials: "include",
    headers: {
      "X-Mobile-App": "talisia",
    },
  },
  exchanges: [
    cacheExchange({
      updates: {
        Mutation: {
          //login update
          login: (_result, _args, cache: Cache, _info) => {
            betterUpdateQuery<LoginMutation, MeQuery>(
              cache,
              { query: MeDocument },
              _result,
              (result, query) => {
                if (result.login.error) return query;
                else return { me: result.login.user };
              }
            );
          },
          //logout update
          logout: (_result, _args, cache: Cache, _info) => {
            betterUpdateQuery<LogoutMutation, MeQuery>(
              cache,
              { query: MeDocument },
              _result,
              () => {
                return { me: null };
              }
            );
          },
          // //reset password update
          resetPassword: (_result, _args, cache: Cache, _info) => {
            betterUpdateQuery<ResetPasswordMutation, MeQuery>(
              cache,
              { query: MeDocument },
              _result,
              (result, query) => {
                if (result.resetPassword.error) return query;
                else return { me: result.resetPassword.user };
              }
            );
          },
        },
      },
    }),
    fetchExchange,
  ],
});

export default client;
