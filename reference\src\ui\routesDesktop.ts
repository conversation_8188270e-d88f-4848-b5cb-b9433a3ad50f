import { ComponentWithAs, IconProps } from "@chakra-ui/react";
import POS from "./views/POS/POS";
import AddItemCategory from "./views/POS/pages/AddItemCategory";
import ProductsPage from "./views/POS/pages/Products";
import ManageProduct from "./views/POS/pages/ManageProduct";
import ViewItemPage from "./views/POS/pages/Product";
import CustomerTags from "./views/POS/pages/CustomerTags";
import Counter from "./views/POS/pages/Counter";
import AddCounter from "./views/POS/pages/AddCounter";
import EditCounter from "./views/POS/pages/EditCounter";
import CountersPage from "./views/POS/pages/Counters";
import PendingOrders from "./views/POS/pages/PendingOrders";
import OpenTabs from "./views/POS/pages/OpenTabs";
import SalesPage from "./views/POS/pages/SalesPage";
import EmployeePage from "./views/POS/pages/Users";
import Profile from "./views/User/Profile";
import AddEmployee from "./views/User/AddEmployee";
import Roles from "./views/POS/pages/roles";
import EditEmployee from "./views/User/EditEmployee";
import POSImport from "./views/POS/pages/Import";
import TransferPage from "./views/POS/pages/TransferProductsStock";
import UserProfile from "./views/POS/pages/User";
import ManageExpenses from "./views/Dashboard/Expense";
import Troubleshoot from "./views/POS/pages/Troubleshoot";

export interface DesktopRoute {
  path?: string;
  name: string;
  icon?: ComponentWithAs<"svg", IconProps> | string;
  component?: React.ComponentType;
  layout?: string;
  category?: string;
  state?: string;
  views?: Array<DesktopRoute>;
  secondaryNavbar?: boolean;
  depth: number;
  appear: boolean;
}

export const desktopRoutes = [
  {
    path: "/POS",
    name: "POS",
    component: POS,
    layout: "/desktop",
    appear: true,
  },
  {
    path: "/counter",
    name: "Counter",
    component: Counter,
    layout: "/desktop",
    appear: false,
  },
  {
    path: "/counters",
    name: "Counters",
    component: CountersPage,
    layout: "/desktop",
    appear: true,
  },
  {
    path: "/add-counter",
    name: "Add Counter",
    component: AddCounter,
    layout: "/desktop",
    appear: false,
  },
  {
    path: "/edit-counter",
    name: "Edit Counter",
    component: EditCounter,
    layout: "/desktop",
    appear: false,
  },
  {
    path: "/import-product",
    name: "Import Product",
    component: POSImport,
    layout: "/desktop",
    appear: true,
  },
  {
    path: "/transfer-stock",
    name: "Transfer Stock",
    component: TransferPage,
    layout: "/desktop",
    appear: true,
  },
  {
    path: "/open-tabs",
    name: "Open Tabs",
    component: OpenTabs,
    layout: "/desktop",
    appear: false,
  },
  {
    path: "/sales",
    name: "Sales",
    component: SalesPage,
    layout: "/desktop",
    appear: true,
  },
  {
    path: "/employee",
    name: "User",
    component: Profile,
    layout: "/desktop",
    appear: false,
  },
  {
    path: "/user",
    name: "User Profile",
    component: UserProfile,
    layout: "/desktop",
    appear: false,
  },
  {
    path: "/update-profile",
    name: "User",
    component: EditEmployee,
    layout: "/desktop",
    appear: false,
  },
  {
    path: "/add-employee",
    name: "Add User",
    component: AddEmployee,
    layout: "/desktop",
    appear: false,
  },
  {
    path: "/employees",
    name: "Users",
    component: EmployeePage,
    layout: "/desktop",
    appear: true,
  },
  {
    path: "/roles",
    name: "Designations",
    component: Roles,
    layout: "/desktop",
    appear: true,
  },
  {
    path: "/categories",
    name: "Categories",
    component: AddItemCategory,
    layout: "/desktop",
    appear: true,
  },
  {
    path: "/products",
    name: "Products",
    component: ProductsPage,
    layout: "/desktop",
    appear: true,
  },
  {
    path: "/product",
    name: "View Product",
    component: ViewItemPage,
    layout: "/desktop",
    appear: false,
  },
  {
    path: "/manage-product",
    name: "Manage Product",
    component: ManageProduct,
    layout: "/desktop",
    appear: false,
  },
  {
    path: "/pending-orders",
    name: "Pending Orders",
    component: PendingOrders,
    layout: "/desktop",
    appear: false,
  },
  {
    path: "/customer-tag",
    name: "Customer Tags",
    component: CustomerTags,
    layout: "/desktop",
    appear: true,
  },
  {
    path: "/expenses",
    name: "Office Expenses",
    component: ManageExpenses,
    layout: "/desktop",
    appear: true,
  },
  {
    path: "/troubleshoot",
    name: "Troubleshoot",
    component: Troubleshoot,
    layout: "/desktop",
    appear: true,
  },
];
