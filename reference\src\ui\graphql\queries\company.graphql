query getCompany($id: Float!) {
  getCompany(id: $id) {
    id
    name
    tinNumber
    registrationNumber
    type
    employees {
      id
      role {
        name
      }
    }
    features {
      id
      name
    }
    location
  }
}

query getCompanies {
  getCompanies {
    id
    name
    tinNumber
    registrationNumber
    type
    location
  }
}

query getSchedules($ownerId: Int!, $owner: String!) {
  getSchedules(ownerId: $ownerId, owner: $owner) {
    id
    onTime
    offTime
    day
    description
    clinicId
    employeeId
  }
}

query syncHistory($companyId: Float!, $entityName: String, $limit: Float) {
  syncHistory(companyId: $companyId, entityName: $entityName, limit: $limit) {
    id
    entityName
    direction
    status
    lastSyncTimestamp
  }
}
