/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useState, useEffect, useContext } from "react";
import {
  Box,
  Flex,
  Heading,
  Text,
  Image,
  VStack,
  Button,
  useColorModeValue,
  HStack,
  Spinner,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  IconButton,
  useDisclosure,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  ModalCloseButton,
  FormControl,
  FormLabel,
  Input,
  NumberInput,
  NumberInputField,
  useToast,
} from "@chakra-ui/react";
import { Link, useHistory, useLocation } from "react-router-dom";
import {
  Item,
  useGetItemStoreStocksPosQuery,
  useAddUnitMutation,
  useUpdateUnitMutation,
  useDeleteUnitMutation,
  useGetItemQuery,
} from "../../../generated/graphql";
import JsBarcode from "jsbarcode";
import { MeContext } from "../../../components/Wrapper";
import { hasPermission } from "../../../interfaces/Helpers";
import { AddIcon, EditIcon, DeleteIcon } from "@chakra-ui/icons";
import { toDateTime } from "../../../utils/Helpers";

interface IViewItemState {
  item: Item;
}

interface UnitFormData {
  name: string;
  quantity: string | number; // Allow both string and number for quantity
  price: number;
}

const ViewItemPage: React.FC = () => {
  const history = useHistory();
  const { state } = useLocation<IViewItemState>();
  const me = useContext(MeContext);

  const toast = useToast({
    position: "top",
  });

  const [product, setProduct] = useState<Item | null>(null);
  const [barcodeImage, setBarcodeImage] = useState<string | null>(null); // Barcode image state
  const [selectedUnit, setSelectedUnit] = useState<any>(null);
  const { isOpen, onOpen, onClose } = useDisclosure();
  const [, addUnit] = useAddUnitMutation();
  const [, updateUnit] = useUpdateUnitMutation();
  const [, deleteUnit] = useDeleteUnitMutation();
  const [formData, setFormData] = useState<UnitFormData>({
    name: "",
    quantity: "1", // Start with string "1"
    price: 0,
  });

  useEffect(() => {
    if (!state || !state?.item || !state.item.id) {
      history.push("products");
    } else {
      setProduct(state.item);
      generateBarcode(state.item);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // get stock for different stores
  const [{ data, fetching }, reFetchItemStoreStocks] =
    useGetItemStoreStocksPosQuery({
      variables: { itemId: product ? product.id : 0 },
      requestPolicy: "network-only",
    });

  const [{ data: itemData, fetching: itemFetching }, reFetchItem] =
    useGetItemQuery({
      variables: { id: product ? product.id : 0 },
      requestPolicy: "network-only",
    });

  useEffect(() => {
    if (itemData?.getItem) {
      setProduct(itemData.getItem as Item);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [itemFetching]);

  useEffect(() => {
    reFetchItemStoreStocks();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [product]);

  const bgCard = useColorModeValue("white", "gray.800");
  const textColor = useColorModeValue("gray.700", "white");
  const accentColor = useColorModeValue("teal.500", "teal.300");
  const bgPage = useColorModeValue("gray.50", "gray.900");

  const generateBarcode = (product: Item) => {
    const canvas = document.createElement("canvas");
    JsBarcode(canvas, `${product.name}`, {
      format: "CODE128",
      lineColor: "#000000", // Black lines
      background: "#ffffff", // White background for contrast
      width: 2,
      height: 100,
      displayValue: true, // Show text under barcode
      fontSize: 18,
      textMargin: 10,
    });
    const barcodeData = canvas.toDataURL("image/png");
    setBarcodeImage(barcodeData);
  };

  const printBarcode = () => {
    const barcodeElement = document.getElementById(
      "barcode"
    ) as HTMLImageElement;
    if (barcodeElement) {
      const printWindow = window.open("", "_blank");
      if (printWindow) {
        const printContent = `
          <html>
            <head>
              <title>Print Barcode</title>
              <style>
                body {
                  font-family: Arial, sans-serif;
                  text-align: center;
                  padding: 20px;
                }
                img {
                  max-width: 100%;
                  height: auto;
                }
              </style>
            </head>
            <body>
              <img src="${barcodeElement.src}" alt="Barcode" id="barcode-image" />
            </body>
          </html>
        `;
        printWindow.document.write(printContent);
        printWindow.document.close();
        const printImage = printWindow.document.getElementById(
          "barcode-image"
        ) as HTMLImageElement;
        if (printImage) {
          printImage.onload = () => {
            printWindow.print();
          };
        }
      }
    }
  };

  const handleAddUnit = async () => {
    try {
      const payload = {
        ...formData,
        quantity: parseFloat(formData.quantity.toString()) || 0, // Convert to number
      };
      const result = await addUnit({
        args: {
          itemId: product!.id,
          ...payload,
        },
      });

      if (result.data?.addUnit.status) {
        toast({
          title: "Unit added successfully",
          status: "success",
          duration: 3000,
        });
        onClose();
        // Refresh product data here
        reFetchItem();
      } else {
        toast({
          title: "Failed to add unit",
          description: result.data?.addUnit.error?.message,
          status: "error",
          duration: 3000,
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "An error occurred while adding the unit",
        status: "error",
        duration: 3000,
      });
    }
  };

  const handleEditUnit = async () => {
    try {
      const payload = {
        ...formData,
        quantity: parseFloat(formData.quantity.toString()) || 0, // Convert to number
      };
      const result = await updateUnit({
        id: selectedUnit.id,
        args: { ...payload, itemId: state.item.id },
      });

      if (result.data?.updateUnit.status) {
        toast({
          title: "Unit updated successfully",
          status: "success",
          duration: 3000,
        });
        onClose();
        // Refresh product data here
        reFetchItem();
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "An error occurred while updating the unit",
        status: "error",
        duration: 3000,
      });
    }
  };

  const handleDeleteUnit = async (unitId: number) => {
    const result = await deleteUnit({ id: unitId });

    if (result.data?.deleteUnit.status) {
      toast({
        title: "Unit deleted successfully",
        status: "success",
        duration: 3000,
      });
      // Refresh product data here
      reFetchItem();
    }
  };

  if (!product) return null;

  return (
    <Flex
      direction="column"
      position="relative"
      flex="1" // Take remaining vertical space
      overflowY="auto"
      maxHeight={{ base: "calc(100vh - 0px)", md: "calc(100vh - 0px)" }}
      sx={{
        "&::-webkit-scrollbar": {
          width: "1px",
        },
        "&::-webkit-scrollbar-thumb": {
          background: "gray.300",
          borderRadius: "4px",
        },
        "&::-webkit-scrollbar-thumb:hover": {
          background: "gray.500",
        },
      }}
    >
      <Flex direction="column" p={10} bg={bgPage}>
        <Heading textAlign="center" mb={10} color={accentColor}>
          Product Details
        </Heading>
        <Flex
          direction={{ base: "column", md: "row" }}
          align="center"
          bg={bgCard}
          boxShadow="lg"
          borderRadius="20px"
          p={6}
          mb={10}
        >
          <Box
            position="relative"
            w={{ base: "100%", md: "300px" }}
            h={{ base: "300px", md: "300px" }}
            overflow="hidden"
            borderRadius="15px"
            boxShadow="md"
            mr={{ md: 8 }}
            mb={{ base: 6, md: 0 }}
          >
            <Image
              src={product.image}
              alt={product.name}
              objectFit="cover"
              w="100%"
              h="100%"
            />
            {/* Optional overlay */}
            <Box
              position="absolute"
              top="0"
              left="0"
              w="100%"
              h="100%"
              bgGradient="linear(to-b, transparent, rgba(0, 0, 0, 0.5))"
            />
          </Box>
          <VStack align="flex-start" spacing={4} flex={1} height={"100%"}>
            <Heading size="lg" color={textColor}>
              {product.name}
            </Heading>

            <HStack spacing={4}>
              <Text fontSize="md" color={textColor} minWidth="150px">
                <b>Generic Name:</b>
              </Text>
              <Text fontSize="md" color={textColor}>
                {product.description}
              </Text>
            </HStack>

            <HStack spacing={4}>
              <Text fontSize="md" color={textColor} minWidth="150px">
                <b>Category:</b>
              </Text>
              <Text fontSize="md" color={textColor}>
                {product.reference || "N/A"}
              </Text>
            </HStack>

            <HStack spacing={4}>
              <Text fontSize="md" color={textColor} minWidth="150px">
                <b>Stock:</b>
              </Text>
              <Text fontSize="md" color={textColor}>
                {product.stock} {product.unit}
              </Text>
            </HStack>

            <HStack spacing={4}>
              <Text fontSize="md" color={textColor} minWidth="150px">
                <b>Unit:</b>
              </Text>
              <Text fontSize="md" color={textColor}>
                {product.unit}
              </Text>
            </HStack>

            <HStack spacing={4}>
              <Text fontSize="md" color={textColor} minWidth="150px">
                <b>Price:</b>
              </Text>
              <Text fontSize="md" color={textColor}>
                {product.sellingPrice} Tsh
              </Text>
            </HStack>

            {hasPermission(me?.permissions, ["Products>Edit"]) && (
              <Button
                as={Link}
                to={{ pathname: "manage-product", state: { item: product } }}
                colorScheme="teal"
                variant="solid"
                size="sm"
                mt={4}
              >
                Edit Product
              </Button>
            )}
          </VStack>
          {/* Below second vertical stack which shows stock for every store/counter */}
          <VStack
            align="flex-start"
            spacing={4}
            flex={1}
            height="100%"
            padding={4}
          >
            <Heading size="lg" color={textColor}>
              Stock by Store/Counter
            </Heading>

            {/* Show loading spinner when data is being fetched */}
            {fetching ? (
              <Box
                display="flex"
                justifyContent="center"
                alignItems="center"
                height="100%"
              >
                <Spinner size="lg" color="teal.500" />
              </Box>
            ) : (
              // Show store stock data when it's available
              data?.getItemStoreStocks.map((storeStock) => (
                <HStack
                  key={storeStock.store.id}
                  spacing={4}
                  width="100%"
                  paddingY={2}
                >
                  <Text
                    fontSize="md"
                    color={textColor}
                    minWidth="150px"
                    fontWeight="bold"
                  >
                    {storeStock.store.name}:
                  </Text>
                  <Text fontSize="md" color={textColor}>
                    {storeStock.stock} {product.unit}
                  </Text>
                </HStack>
              ))
            )}

            {/* Show a message when no data is available */}
            {!fetching && (!data || data.getItemStoreStocks.length === 0) && (
              <Text color={textColor} fontSize="md">
                No store stock data available.
              </Text>
            )}
          </VStack>
        </Flex>
        <Flex direction="column" align="center">
          <Heading size="md" mb={4} color={textColor}>
            Barcode
          </Heading>
          <Box
            p={4}
            bg="white"
            border="1px solid #ddd"
            borderRadius="md"
            textAlign="center"
            mb={4}
          >
            {barcodeImage ? (
              <Image id="barcode" src={barcodeImage} alt="Barcode" />
            ) : (
              <Text>Loading Barcode...</Text>
            )}
          </Box>
          <Button colorScheme="teal" onClick={printBarcode}>
            Print Barcode
          </Button>
        </Flex>
        <VStack align="flex-start" spacing={4} width="100%" mt={8}>
          <Flex width="100%" justify="space-between" align="center">
            <Heading size="lg" color={textColor}>
              Product Units
            </Heading>
            {hasPermission(me?.permissions, ["Products>Edit"]) && (
              <Button
                leftIcon={<AddIcon />}
                colorScheme="teal"
                size="sm"
                onClick={() => {
                  setSelectedUnit(null);
                  setFormData({ name: "", quantity: "1", price: 0 });
                  onOpen();
                }}
              >
                Add Unit
              </Button>
            )}
          </Flex>

          <Table variant="simple" size="sm">
            <Thead>
              <Tr>
                <Th>Name</Th>
                <Th>Quantity</Th>
                <Th>Price</Th>
                <Th>Actions</Th>
              </Tr>
            </Thead>
            <Tbody>
              {product.units?.map((unit) => (
                <Tr key={unit.id}>
                  <Td>{unit.name}</Td>
                  <Td>{unit.quantity}</Td>
                  <Td>{unit.price} Tsh</Td>
                  <Td>
                    <HStack spacing={2}>
                      <IconButton
                        aria-label="Edit unit"
                        icon={<EditIcon />}
                        size="sm"
                        colorScheme="blue"
                        onClick={() => {
                          setSelectedUnit(unit);
                          setFormData({
                            name: unit.name,
                            quantity: unit.quantity,
                            price: unit.price,
                          });
                          onOpen();
                        }}
                      />
                      <IconButton
                        aria-label="Delete unit"
                        icon={<DeleteIcon />}
                        size="sm"
                        colorScheme="red"
                        onClick={() => handleDeleteUnit(unit.id)}
                      />
                    </HStack>
                  </Td>
                </Tr>
              ))}
            </Tbody>
          </Table>
        </VStack>

        {/* Add/Edit Unit Modal */}
        <Modal isOpen={isOpen} onClose={onClose}>
          <ModalOverlay />
          <ModalContent>
            <ModalHeader>{selectedUnit ? "Edit Unit" : "Add Unit"}</ModalHeader>
            <ModalCloseButton />
            <ModalBody>
              <VStack spacing={4}>
                <FormControl>
                  <FormLabel>Unit Name</FormLabel>
                  <Input
                    value={formData.name}
                    onChange={(e) =>
                      setFormData({ ...formData, name: e.target.value })
                    }
                    placeholder="e.g., Box, Carton"
                  />
                </FormControl>
                <FormControl>
                  <FormLabel>Quantity</FormLabel>
                  <Input
                    type="text"
                    value={
                      typeof formData.quantity === "number"
                        ? formData.quantity.toString()
                        : formData.quantity
                    }
                    onChange={(e) => {
                      const value = e.target.value;
                      // Allow empty string, numbers, and one decimal point
                      if (value === "" || /^\d*\.?\d*$/.test(value)) {
                        setFormData({ ...formData, quantity: value });
                      }
                    }}
                    placeholder="0.00"
                  />
                </FormControl>
                <FormControl>
                  <FormLabel>Price (Tsh)</FormLabel>
                  <NumberInput
                    value={formData.price}
                    onChange={(_, value) =>
                      setFormData({ ...formData, price: value })
                    }
                    min={0}
                  >
                    <NumberInputField />
                  </NumberInput>
                </FormControl>
              </VStack>
            </ModalBody>
            <ModalFooter>
              <Button variant="ghost" mr={3} onClick={onClose}>
                Cancel
              </Button>
              <Button
                colorScheme="blue"
                onClick={selectedUnit ? handleEditUnit : handleAddUnit}
              >
                {selectedUnit ? "Save Changes" : "Add Unit"}
              </Button>
            </ModalFooter>
          </ModalContent>
        </Modal>

        <VStack align="flex-start" spacing={4} width="100%" mt={8}>
          <Heading size="lg" color={textColor}>
            Import History
          </Heading>
          <Table variant="simple" size="sm">
            <Thead>
              <Tr>
                <Th>Date</Th>
                <Th>Supplier</Th>
                <Th>Quantity</Th>
                <Th>Import Price</Th>
                <Th>Selling Price</Th>
              </Tr>
            </Thead>
            <Tbody>
              {product?.imports?.map((import_) => (
                <Tr key={import_.id}>
                  <Td>{toDateTime(Number(import_.importDate))}</Td>
                  <Td>{import_.supplier}</Td>
                  <Td>{import_.quantity}</Td>
                  <Td>{import_.importPrice} Tsh</Td>
                  <Td>{import_.sellingPrice} Tsh</Td>
                </Tr>
              ))}
              {(!product?.imports || product.imports.length === 0) && (
                <Tr>
                  <Td colSpan={5} textAlign="center">
                    No import history found
                  </Td>
                </Tr>
              )}
            </Tbody>
          </Table>
        </VStack>

        <VStack align="flex-start" spacing={4} width="100%" mt={8}>
          <Heading size="lg" color={textColor}>
            Product Stock Transfers
          </Heading>
          <Table variant="simple" size="sm">
            <Thead>
              <Tr>
                <Th>Date</Th>
                <Th>Type</Th>
                <Th>Details</Th>
                <Th>Source Store</Th>
                <Th>Destination Store</Th>
                <Th>Quantity</Th>
              </Tr>
            </Thead>
            <Tbody>
              {product?.inventoryTransfers?.map((transfer) => {
                // Find corresponding transfer quantity
                const transferQuantity =
                  product.transfers?.find((t) => t.inventoryId === transfer.id)
                    ?.quantity || 0;

                return (
                  <Tr key={transfer.id}>
                    <Td>{toDateTime(Number(transfer.transferDate))}</Td>
                    <Td>{transfer.type}</Td>
                    <Td>{transfer.details}</Td>
                    <Td>{transfer.sourceStoreId}</Td>
                    <Td>{transfer.destinationStoreId}</Td>
                    <Td>{transferQuantity}</Td>
                  </Tr>
                );
              })}
              {(!product?.inventoryTransfers ||
                product.inventoryTransfers.length === 0) && (
                <Tr>
                  <Td colSpan={6} textAlign="center">
                    No transfer history found
                  </Td>
                </Tr>
              )}
            </Tbody>
          </Table>
        </VStack>
      </Flex>
    </Flex>
  );
};

export default ViewItemPage;
