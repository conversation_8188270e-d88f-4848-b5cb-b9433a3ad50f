/* eslint-disable @typescript-eslint/no-empty-object-type */
/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useContext, useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import {
  Flex,
  Button,
  FormControl,
  FormLabel,
  Input,
  Text,
  useColorModeValue,
  FormErrorMessage,
  useToast,
  Grid,
  GridItem,
  Box,
  Stack,
  Progress,
  Icon,
  Spinner,
} from "@chakra-ui/react";
import { useLocation } from "react-router-dom";
import { MdCheck } from "react-icons/md";
import {
  Item,
  useImportItemMutation,
  useGetMerchandiseItemsQuery,
} from "../../../generated/graphql";
import { ActionType } from "../../../interfaces/Types";
import { formatToMoney } from "../../../utils/Helpers";
import { AppContext } from "../../../AppContext";
import { AsyncSelect } from "chakra-react-select";
import POSFileInput from "../components/FileInput";
import { HSeparator } from "../components/Separator";
import UnitInput from "../components/UnitInput";

interface IImportItemProps {}

interface IRecentImports {
  name: string;
  quantity: number;
  importPrice: number;
  sellingPrice: number;
  unit: string;
}

interface IRecentImport {
  id: number;
  name: string;
  quantity: number;
  importPrice: number;
  sellingPrice: number;
  unit: string;
  loading: boolean;
}

interface State {
  item: Item;
}

const POSImport: React.FC<IImportItemProps> = () => {
  const toast = useToast({ position: "top" });
  const bgForm = useColorModeValue("white", "navy.800");
  const bgColor = useColorModeValue("#F8F9FA", "navy.900");
  const nameColor = useColorModeValue("gray.500", "white");
  const subtitleTextColor = useColorModeValue("black.400", "whiteAlpha.700");
  const { dispatch, state } = useContext(AppContext);

  const [, importItem] = useImportItemMutation();
  const [{ data: items, fetching }, getItemsAgain] =
    useGetMerchandiseItemsQuery();
  const [error, seterror] = useState("");
  const location = useLocation();
  const [clearFileInput, setClearFileInput] = useState(false);
  const [receiptUrl, setReceiptUrlState] = useState("");
  const [fileLoading, setFileLoading] = useState(false);
  const [fileInputError, setFileInputError] = useState("");
  const [imported, setImported] = useState<IRecentImports[]>([]);
  const [importing, setImporting] = useState<IRecentImport | null>(null);
  const [selectedItem, setSelectedItem] = useState<number | null>(null);
  const [unitType, setUnitType] = useState("");
  const [submitting, setSubmitting] = useState<number | null>(null);
  const [itemOptions, setItemOptions] = useState<
    { label: string; value: number }[]
  >([]);
  const [selectedItems, setSelectedItems] = useState<Item[]>([]);
  const locationState = location.state as State;
  const [itemType, setItemType] = useState(
    locationState?.item?.type ? locationState.item.type : ""
  );

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors, isSubmitting },
    setValue,
  } = useForm();

  useEffect(() => {
    if (
      state.pendingFile?.path === location.pathname &&
      state.pendingFile.uploadingFileUrl
    ) {
      setReceiptUrlState(state.pendingFile.uploadingFileUrl);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const loadItems = async (searchInput: string) => {
    if (searchInput.length > 0) {
      if (!fetching && items) {
        return items.getMerchandiseItems
          .filter(
            (item) =>
              item.name
                .toLocaleLowerCase()
                .includes(searchInput.toLocaleLowerCase()) &&
              !selectedItems.some((ii) => ii.id === item.id)
          )
          .map((r) => ({
            label: r.name,
            value: r.id,
          }));
      }
      await getItemsAgain({ requestPolicy: "network-only" });
      if (items) {
        return items.getMerchandiseItems
          .filter((item) =>
            item.name
              .toLocaleLowerCase()
              .includes(searchInput.toLocaleLowerCase())
          )
          .map((r) => ({
            label: r.name,
            value: r.id,
          }));
      }
    } else {
      if (!fetching && items)
        return items.getMerchandiseItems
          .filter((item) => !selectedItems.some((ii) => ii.id === item.id))
          .map((r) => ({
            label: r.name,
            value: r.id,
          }));
    }
    return [];
  };

  const handleItemChange = async (value: any) => {
    if (items && value) {
      setSelectedItem(value.value);
      setValue("itemId", value.value);
      const ourItem = items?.getMerchandiseItems.find(
        (item) => item.id === value.value
      );
      if (ourItem) {
        setItemType(ourItem.type);
        setSelectedItems((oldItems) => {
          if (!oldItems.some((item) => item.id === ourItem.id)) {
            return [...oldItems, ourItem] as Item[];
          }
          return oldItems;
        });
      }
    } else {
      setSelectedItem(null);
    }
    reset();
  };

  const handleUnitChange: any = (value: any) => {
    if (value) {
      setUnitType(value.value);
      setValue("unit", value.value);
    } else {
      setUnitType("");
    }
  };

  const setReceiptUrl: any = (url: string) => {
    setReceiptUrlState(url);
    dispatch({
      type: ActionType.SET_UPLOADING_FILE_URL,
      pendingFile: {
        uploadingFileUrl: url,
        path: location.pathname,
      },
    });
  };

  async function onSubmit(values: any) {
    seterror("");
    if (!selectedItem) throw new Error("Item is required! Select Item first!");
    setSubmitting(selectedItem);
    const args = {
      itemId: Number(selectedItem),
      supplier: values.supplier,
      importDate: values.importDate,
      // Removed expireDate since we do not track batches.
      unit: unitType,
      quantity: Number(values.quantity),
      importPrice: Number(values.importPrice) ? Number(values.importPrice) : 0,
      sellingPrice: Number(values.sellingPrice)
        ? Number(values.sellingPrice)
        : 0,
      pieceSellingPrice: Number(values.pieceSellingPrice)
        ? Number(values.pieceSellingPrice)
        : 0,
      subPieceSellingPrice: Number(values.subPieceSellingPrice)
        ? Number(values.subPieceSellingPrice)
        : 0,
      receipt: receiptUrl,
      // Removed batch field.
    };
    setImporting({
      id: Number(selectedItem),
      name:
        items?.getMerchandiseItems.find(
          (item) => item.id === Number(selectedItem)
        )?.name || "",
      quantity: args.quantity,
      unit: args.unit,
      importPrice: args.importPrice,
      sellingPrice: args.sellingPrice,
      loading: true,
    });
    const { data } = await importItem({ args });
    setSubmitting(null);
    if (data?.importItem.error) {
      console.log("The error came back: ", data.importItem.error.message);
      setImporting(null); // Clear pending import on error
      seterror(data.importItem.error.message);
      return;
    } else {
      reset();
      setClearFileInput(true);
      setImported((importedItems) => [
        {
          name:
            items?.getMerchandiseItems.find(
              (item) => item.id === Number(selectedItem)
            )?.name || "",
          quantity: args.quantity,
          unit: args.unit,
          importPrice: args.importPrice,
          sellingPrice: args.sellingPrice,
        },
        ...importedItems,
      ]);
      setImporting(null);
      toast({
        title: `${
          args.quantity +
          " " +
          items?.getMerchandiseItems.find((item) => item.id === args.itemId)
            ?.unit +
          " of " +
          items?.getMerchandiseItems.find((item) => item.id === args.itemId)
            ?.name
        } was imported successfully!`,
        variant: "left-accent",
        status: "success",
        isClosable: true,
      });
    }
  }

  const fileError = (value: string): void => {
    setFileInputError(value);
  };

  // Update itemsOptions when query data is ready
  useEffect(() => {
    if (items && !fetching) {
      const options = items.getMerchandiseItems.map((r) => ({
        label: r.name,
        value: r.id,
      }));
      setItemOptions(options);
    }
  }, [items, fetching]);

  return (
    <Flex
      direction="column"
      p={4}
      bg={useColorModeValue("gray.50", "gray.900")}
      minH="100vh"
    >
      <Flex direction="row" justifyContent="space-between" mb={6}>
        <Text
          fontSize="3xl"
          fontWeight="extrabold"
          color={useColorModeValue("blue.900", "blue.200")}
        >
          POS Item Import
        </Text>
        <Button colorScheme="red" onClick={() => reset()}>
          Reset Form
        </Button>
      </Flex>
      <FormErrorMessage>{error}</FormErrorMessage>
      <Flex direction="row" mb={6} gap={4}>
        <Box flex="1" p={4} bg={bgForm} borderRadius="15px" boxShadow="lg">
          <Text fontSize="xl" mb={4} textAlign="center">
            Import Item Details
          </Text>
          <form onSubmit={handleSubmit(onSubmit)}>
            <Grid templateColumns="repeat(4, 1fr)" gap={4}>
              <FormControl
                as={GridItem}
                colSpan={4}
                isInvalid={errors.item as any}
              >
                <AsyncSelect
                  variant="flushed"
                  isClearable
                  isSearchable={true}
                  isLoading={fetching}
                  defaultOptions={itemOptions}
                  loadOptions={loadItems}
                  size="sm"
                  placeholder="Select item"
                  closeMenuOnSelect={true}
                  onChange={handleItemChange}
                  escapeClearsValue={true}
                  hideSelectedOptions={true}
                  styles={{
                    container: (base) => ({
                      ...base,
                      width: "100%",
                      color: "navy",
                    }),
                  }}
                />
                <FormErrorMessage>
                  {errors.item && (errors.item.message as any)}
                </FormErrorMessage>
              </FormControl>
              {itemType !== "" && (
                <>
                  <FormControl
                    as={GridItem}
                    colSpan={2}
                    isInvalid={errors.importDate as any}
                  >
                    <FormLabel htmlFor="importDate" fontSize="xs">
                      Import Date
                    </FormLabel>
                    <Input
                      variant="filled"
                      type="date"
                      fontSize="xs"
                      placeholder="Import date"
                      size="lg"
                      id="importDate"
                      {...register("importDate", {
                        required: "This is required",
                      })}
                    />
                    <FormErrorMessage>
                      {errors.importDate && (errors.importDate.message as any)}
                    </FormErrorMessage>
                  </FormControl>
                  <FormControl
                    as={GridItem}
                    colSpan={2}
                    isInvalid={errors.supplier as any}
                  >
                    <FormLabel htmlFor="supplier" fontSize="xs">
                      Supplier
                    </FormLabel>
                    <Input
                      variant="filled"
                      type="text"
                      fontSize="xs"
                      placeholder="Eg: Supplier Info"
                      size="lg"
                      id="supplier"
                      {...register("supplier", {
                        required: "This is required",
                      })}
                    />
                    <FormErrorMessage>
                      {errors.supplier && (errors.supplier.message as any)}
                    </FormErrorMessage>
                  </FormControl>
                  <FormControl
                    as={GridItem}
                    colSpan={2}
                    isInvalid={errors.quantity as any}
                  >
                    <FormLabel htmlFor="quantity" fontSize="xs">
                      Import Quantity
                    </FormLabel>
                    <Input
                      variant="filled"
                      type="number"
                      fontSize="xs"
                      placeholder="Imported quantity"
                      size="lg"
                      id="quantity"
                      {...register("quantity", {
                        required: "This is required",
                      })}
                    />
                    <FormErrorMessage>
                      {errors.quantity && (errors.quantity.message as any)}
                    </FormErrorMessage>
                  </FormControl>
                  <FormControl
                    as={GridItem}
                    colSpan={2}
                    isInvalid={errors.unit as any}
                  >
                    <FormLabel htmlFor="unit" fontSize="xs">
                      Select Unit
                    </FormLabel>
                    <UnitInput
                      onChange={handleUnitChange}
                      selectedItem={
                        items?.getMerchandiseItems.find(
                          (item) => item.id === selectedItem
                        ) as Item
                      }
                    />
                    <FormErrorMessage>
                      {errors.unit && (errors.unit.message as any)}
                    </FormErrorMessage>
                  </FormControl>
                  <FormControl
                    as={GridItem}
                    colSpan={2}
                    isInvalid={errors.importPrice as any}
                  >
                    <FormLabel htmlFor="importPrice" fontSize="xs">
                      Import Price Per Item
                    </FormLabel>
                    <Input
                      variant="filled"
                      type="number"
                      fontSize="xs"
                      placeholder="Import price per item"
                      size="lg"
                      id="importPrice"
                      {...register("importPrice", {
                        required: "This is required",
                      })}
                    />
                    <FormErrorMessage>
                      {errors.importPrice &&
                        (errors.importPrice.message as any)}
                    </FormErrorMessage>
                  </FormControl>
                  <FormControl
                    as={GridItem}
                    colSpan={2}
                    isInvalid={errors.sellingPrice as any}
                  >
                    <FormLabel htmlFor="sellingPrice" fontSize="xs">
                      Selling Price Per Item
                    </FormLabel>
                    <Input
                      variant="filled"
                      type="number"
                      fontSize="xs"
                      placeholder="Selling price per item"
                      size="lg"
                      id="sellingPrice"
                      {...register("sellingPrice", {
                        required: "This is required",
                      })}
                    />
                    <FormErrorMessage>
                      {errors.sellingPrice &&
                        (errors.sellingPrice.message as any)}
                    </FormErrorMessage>
                  </FormControl>
                  <FormControl
                    as={GridItem}
                    colSpan={4}
                    isInvalid={fileInputError !== ""}
                  >
                    <FormLabel htmlFor="receipt" fontSize="xs">
                      Item Import Receipt (Optional)
                    </FormLabel>
                    <POSFileInput
                      setFileUrl={setReceiptUrl}
                      defaultUrl={receiptUrl}
                      label="Receipt File"
                      clear={clearFileInput}
                      setError={fileError}
                      defaultClear={(value) => setClearFileInput(value)}
                      setLoading={(value) => setFileLoading(value)}
                    />
                    <FormErrorMessage>{fileInputError}</FormErrorMessage>
                  </FormControl>
                </>
              )}
            </Grid>
            {selectedItem && (
              <Button
                fontSize="14px"
                variant="outline"
                fontWeight="bold"
                w="100%"
                h="45"
                mb="24px"
                mt={4}
                colorScheme="teal"
                isLoading={isSubmitting}
                isDisabled={itemType === "" || fileLoading}
                type="submit"
              >
                Import Item
              </Button>
            )}
          </form>
        </Box>
        <Box
          flex="1"
          p={4}
          bg={bgForm}
          borderRadius="15px"
          boxShadow="lg"
          maxW={400}
        >
          <Text
            fontSize="xl"
            color={useColorModeValue("blue.900", "blue.200")}
            fontWeight="bold"
            textAlign="center"
            mb="12px"
          >
            Recently Imported Items
          </Text>
          <HSeparator mb="22px" mt="6px" />
          <Flex direction="column" w="100%">
            {importing && importing.loading && (
              <Box p={5} bg={bgColor} my="5px" borderRadius="12px">
                <Flex justify="space-between" w="100%">
                  <Flex maxWidth="70%" flexDirection="column">
                    <Text color={subtitleTextColor} fontSize="m">
                      Item:
                    </Text>
                    <Text color={nameColor} fontSize="m" fontWeight="bold">
                      {importing.name}
                    </Text>
                  </Flex>
                  <Flex
                    direction={{ sm: "column", md: "column" }}
                    align="flex-start"
                  >
                    <Text color={subtitleTextColor} fontSize="m">
                      Quantity:
                    </Text>
                    <Text color={nameColor} fontSize="m" fontWeight="bold">
                      {importing.quantity + " " + importing.unit}
                    </Text>
                  </Flex>
                </Flex>
                <Flex justify="center">
                  {submitting && submitting === importing.id && (
                    <Stack direction="row" h={3}>
                      <Progress
                        size="s"
                        w={10}
                        isIndeterminate
                        style={{ transform: "rotate(180deg)" }}
                      />
                      <Spinner size="s" />
                      <Progress w={10} size="s" isIndeterminate />
                    </Stack>
                  )}
                </Flex>
                <Flex justify="space-between" w="100%">
                  <Flex maxWidth="70%" flexDirection="column">
                    <Text color={subtitleTextColor} fontSize="m">
                      Import Price:
                    </Text>
                    <Text color={nameColor} fontSize="m" fontWeight="bold">
                      {formatToMoney(importing.importPrice)}
                    </Text>
                  </Flex>
                  <Flex
                    direction={{ sm: "column", md: "column" }}
                    align="flex-start"
                  >
                    <Text color={subtitleTextColor} fontSize="m">
                      Selling Price:
                    </Text>
                    <Text color={nameColor} fontSize="m" fontWeight="bold">
                      {formatToMoney(importing.sellingPrice)}
                    </Text>
                  </Flex>
                </Flex>
              </Box>
            )}
            {imported.length === 0 && !(importing && importing.loading) ? (
              <Text pl={3}>You have not imported items on this page!</Text>
            ) : (
              imported.map((row, idx) => (
                <Box key={idx} p={5} bg={bgColor} my="5px" borderRadius="12px">
                  <Flex justify="space-between" w="100%">
                    <Flex maxWidth="70%" flexDirection="column">
                      <Text color={subtitleTextColor} fontSize="m">
                        Item:
                      </Text>
                      <Text color={nameColor} fontSize="m" fontWeight="bold">
                        {row.name}
                      </Text>
                    </Flex>
                    <Flex
                      direction={{ sm: "column", md: "column" }}
                      align="flex-start"
                    >
                      <Text color={subtitleTextColor} fontSize="m">
                        Quantity:
                      </Text>
                      <Text color={nameColor} fontSize="m" fontWeight="bold">
                        {row.quantity + " " + row.unit}
                      </Text>
                    </Flex>
                  </Flex>
                  <Flex justify="center">
                    <Box
                      w="25px"
                      h="25px"
                      borderRadius="50%"
                      bg="green.500"
                      position="relative"
                    >
                      <Box
                        w="20px"
                        h="3px"
                        bg="green.500"
                        position="absolute"
                        top="50%"
                        left="-50%"
                      />
                      <Icon
                        as={MdCheck}
                        w="20px"
                        h="20px"
                        color="white"
                        position="absolute"
                        top="50%"
                        left="50%"
                        transform="translate(-50%, -50%)"
                      />
                      <Box
                        w="20px"
                        h="3px"
                        bg="green.500"
                        position="absolute"
                        top="50%"
                        right="-50%"
                      />
                    </Box>
                  </Flex>
                  <Flex justify="space-between" w="100%">
                    <Flex maxWidth="70%" flexDirection="column">
                      <Text color={subtitleTextColor} fontSize="m">
                        Import Price:
                      </Text>
                      <Text color={nameColor} fontSize="m" fontWeight="bold">
                        {formatToMoney(row.importPrice)}
                      </Text>
                    </Flex>
                    <Flex
                      direction={{ sm: "column", md: "column" }}
                      align="flex-start"
                    >
                      <Text color={subtitleTextColor} fontSize="m">
                        Selling Price:
                      </Text>
                      <Text color={nameColor} fontSize="m" fontWeight="bold">
                        {formatToMoney(row.sellingPrice)}
                      </Text>
                    </Flex>
                  </Flex>
                </Box>
              ))
            )}
          </Flex>
        </Box>
      </Flex>
    </Flex>
  );
};

export default POSImport;
