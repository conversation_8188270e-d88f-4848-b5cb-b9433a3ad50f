/* eslint-disable @typescript-eslint/no-explicit-any */
import { contextBridge, ipc<PERSON><PERSON><PERSON> } from "electron";

contextBridge.exposeInMainWorld("electron", {
  // ... your existing exposed APIs ...

  // Add these new methods
  invoke: (channel: string, data?: any) => {
    const validChannels = [
      "check-for-updates",
      "start-update",
      "clear-image-cache",
      "save-company-id",
      "get-company-id",
      "save-company-sub",
      "get-company-sub",
      "check-network-status",
      "save-deployment-type",
      "get-deployment-type",
      "scan-network-for-servers",
      "save-server-url",
      "get-server-url",
    ];
    if (validChannels.includes(channel)) {
      return ipcRenderer.invoke(channel, data);
    }
  },
  on: (channel: string, callback: (...args: any[]) => void) => {
    const validChannels = [
      "update-available",
      "update-not-available",
      "download-progress",
      "update-downloaded",
      "update-error",
      "network-status-change",
    ];
    if (validChannels.includes(channel)) {
      ipcRenderer.on(channel, (event, ...args) => callback(...args));
    }
  },
  removeAllListeners: (channel: string) => {
    const validChannels = [
      "update-available",
      "update-not-available",
      "download-progress",
      "update-downloaded",
      "update-error",
      "network-status-change",
    ];
    if (validChannels.includes(channel)) {
      ipcRenderer.removeAllListeners(channel);
    }
  },
});
