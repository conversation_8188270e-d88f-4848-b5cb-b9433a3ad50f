import { Link, Stack } from "expo-router";
import { StyleSheet, View, Text } from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import { useMeContext } from "@/contexts/MeContext";

export default function NotFoundScreen() {
  const { me } = useMeContext();
  let homeHref = "/(tabs)/dashboard";
  if (me?.company?.id === 0) {
    homeHref = "/(tabs)/admin";
  } else if (me?.role?.name === "employee") {
    homeHref = "/(tabs)/employee";
  }
  return (
    <>
      <Stack.Screen options={{ title: "Oops!" }} />
      <LinearGradient
        colors={["#1E1B4B", "#1E40AF", "#4C1D95"]}
        style={styles.container}
      >
        <View style={styles.content}>
          <Text style={styles.title}>This screen doesn't exist.</Text>
          <Link href={homeHref} style={styles.link}>
            <Text style={styles.linkText}>Go to home screen!</Text>
          </Link>
        </View>
      </LinearGradient>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    padding: 20,
  },
  title: {
    color: "#FFFFFF",
    fontSize: 24,
    fontWeight: "600",
    marginBottom: 20,
  },
  link: {
    marginTop: 15,
    paddingVertical: 15,
  },
  linkText: {
    color: "#60A5FA",
    fontSize: 16,
    fontWeight: "500",
  },
});
