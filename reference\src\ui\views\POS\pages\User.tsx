import { Link, useLocation } from "react-router-dom";
import {
  Ava<PERSON>,
  Box,
  Button,
  Flex,
  Text,
  useColorModeValue,
  useToast,
  Input,
  VStack,
  HStack,
  Divider,
  Icon,
} from "@chakra-ui/react";
import { useEffect, useState, ChangeEvent, useRef } from "react";
import { v4 } from "uuid";
import {
  FaEdit,
  FaEnvelope,
  FaPhone,
  FaUser,
  FaLock,
  FaEye,
  FaEyeSlash,
} from "react-icons/fa";
import {
  useEditUserMutation,
  useGetUserQuery,
  useChangePasswordMutation,
  User,
} from "../../../generated/graphql";
import supabase from "../../../supabase";

interface propState {
  employee: User;
}

const UserProfile = () => {
  const state = useLocation().state as propState;
  const userProp = state.employee;

  const [user, setUser] = useState<User | null>(userProp);
  const [editingField, setEditingField] = useState<string | null>(null);
  const [editedValues, setEditedValues] = useState<Partial<User>>({});
  const [hasChanges, setHasChanges] = useState(false);
  const [profileImage, setProfileImage] = useState<string | null>(
    userProp?.image || null
  );
  const [oldImagePath, setOldImagePath] = useState<string | null>(null);

  // Password change states
  const [currentPassword, setCurrentPassword] = useState("");
  const [newPassword, setNewPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [passwordError, setPasswordError] = useState<string | null>(null);
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const fileInputRef = useRef<HTMLInputElement>(null);
  const toast = useToast({ position: "top" });
  const [, editUser] = useEditUserMutation();
  const [, changePassword] = useChangePasswordMutation();

  const [{ data, fetching }] = useGetUserQuery({
    variables: { id: user ? user.id : 0 },
    requestPolicy: "network-only",
  });

  useEffect(() => {
    if (!fetching && data?.getUser) {
      setUser(data.getUser as User);
      if (data.getUser.image) {
        setProfileImage(data.getUser.image);
        // Extract the path for potential deletion later
        try {
          const url = new URL(data.getUser.image);
          const pathMatch = url.pathname.match(/\/heal\/(.+)$/);
          if (pathMatch && pathMatch[1]) {
            setOldImagePath(pathMatch[1]);
          }
        } catch (error) {
          console.error("Error parsing image URL:", error);
          // If URL parsing fails, try the old method
          const parts = data.getUser.image.split("/heal/");
          if (parts.length === 2) {
            setOldImagePath(parts[1]);
          }
        }
      }
    }
  }, [fetching, data]);

  const handleFieldClick = (field: string) => {
    setEditingField(field);
    setEditedValues({ ...editedValues, [field]: user?.[field as keyof User] });
  };

  const handleInputChange = (field: string, value: string) => {
    setEditedValues({ ...editedValues, [field]: value });
    setHasChanges(true);
  };

  const handleSaveField = () => {
    setEditingField(null);
    // Keep the edited values in the state
  };

  const handleCancelEdit = () => {
    setEditingField(null);
  };

  const handleFileChange = async (event: ChangeEvent<HTMLInputElement>) => {
    const selectedFile = event.target.files?.[0];
    if (selectedFile && user?.id) {
      try {
        // Show preview immediately
        const previewUrl = URL.createObjectURL(selectedFile);
        setProfileImage(previewUrl);

        // Create a unique filename with user ID for better organization
        const fileExt = selectedFile.name.split(".").pop();
        const fileName = `${user.id}_${v4()}.${fileExt}`;
        const filePath = `public/${fileName}`;

        // Upload to Supabase
        const { data: uploadedFile, error: uploadError } =
          await supabase.storage.from("heal").upload(filePath, selectedFile);

        if (uploadError) {
          toast({
            title: "Image could not be uploaded!",
            description: uploadError.message,
            status: "error",
            isClosable: true,
          });
          // Revert to previous image if upload fails
          setProfileImage(user.image || null);
          return;
        }

        if (uploadedFile) {
          const fileUrl = `https://eqzgvivfuzmyfxbupxht.supabase.co/storage/v1/object/public/heal/${uploadedFile.path}`;
          const { data, error } = await editUser({
            id: user.id,
            params: {
              image: fileUrl,
              firstname: user.firstname || "",
              middlename: user.middlename || "",
              lastname: user.lastname || "",
              email: user.email || "",
              phone: user.phone || "",
            },
          });

          if (error || !data?.editUser?.status) {
            toast({
              title: "Failed to update profile image",
              status: "error",
              isClosable: true,
            });
            return;
          }

          if (oldImagePath) {
            supabase.storage
              .from("heal")
              .remove([oldImagePath])
              .catch((err) =>
                console.error("Failed to delete old image:", err.message)
              );
          }

          setOldImagePath(uploadedFile.path);
          setUser((prev) => (prev ? { ...prev, image: fileUrl } : null));
          toast({
            title: "Profile image updated successfully!",
            status: "success",
            isClosable: true,
          });
        }
      } catch (error) {
        console.error("Error uploading image:", error);
        toast({
          title: "Error uploading image",
          status: "error",
          isClosable: true,
        });
        // Revert to previous image if there's an error
        setProfileImage(user.image || null);
      }
    }
  };

  const triggerFileSelect = () => {
    fileInputRef.current?.click();
  };

  const handleSave = async () => {
    if (!user?.id || !hasChanges) return;

    try {
      const { data, error } = await editUser({
        id: user.id,
        params: {
          firstname: editedValues.firstname || user?.firstname || "",
          middlename: editedValues.middlename || user?.middlename || "",
          lastname: editedValues.lastname || user?.lastname || "",
          email: editedValues.email || user?.email || "",
          phone: editedValues.phone || user?.phone || "",
          image: user.image || "",
        },
      });

      if (error || !data?.editUser?.status) {
        toast({
          title: "Failed to update profile",
          status: "error",
          isClosable: true,
        });
        return;
      }

      setUser((prev) => (prev ? { ...prev, ...editedValues } : null));
      setHasChanges(false);
      setEditedValues({});
      toast({
        title: "Profile updated successfully!",
        status: "success",
        isClosable: true,
      });
    } catch (error) {
      console.error("Error updating profile:", error);
      toast({
        title: "Error updating profile",
        status: "error",
        isClosable: true,
      });
    }
  };

  const bgColor = useColorModeValue("gray.50", "gray.800");
  const cardBg = useColorModeValue("white", "gray.700");
  const textColor = useColorModeValue("gray.800", "white");
  const secondaryTextColor = useColorModeValue("gray.600", "gray.300");
  const borderColor = useColorModeValue("gray.200", "gray.600");

  return (
    <Flex bg={bgColor} minH="100vh" py={8} px={4}>
      <Box maxW="4xl" mx="auto" w="full">
        {/* Profile Header */}
        <Box
          bg={cardBg}
          borderRadius="lg"
          boxShadow="md"
          p={6}
          mb={6}
          borderWidth={1}
          borderColor={borderColor}
        >
          <Flex direction={{ base: "column", md: "row" }} align="center">
            <Box position="relative" mb={{ base: 4, md: 0 }}>
              <Avatar
                src={
                  profileImage ||
                  user?.image ||
                  "https://via.placeholder.com/150?text=No+Image"
                }
                size="2xl"
                borderRadius="full"
                borderWidth={2}
                borderColor={borderColor}
              />
              <Button
                size="sm"
                colorScheme="blue"
                position="absolute"
                bottom={2}
                right={2}
                borderRadius="full"
                onClick={triggerFileSelect}
                leftIcon={<FaEdit />}
              >
                Edit
              </Button>
              <Input
                ref={fileInputRef}
                type="file"
                accept="image/*"
                display="none"
                onChange={handleFileChange}
              />
            </Box>

            <VStack align="start" spacing={3} ml={{ md: 6 }} flex={1}>
              <Text fontSize="2xl" fontWeight="bold" color={textColor}>
                {editedValues.firstname || user?.firstname}{" "}
                {editedValues.middlename || user?.middlename}{" "}
                {editedValues.lastname || user?.lastname}
              </Text>
              <Text fontSize="md" color={secondaryTextColor}>
                {user?.role?.name || "User"}
              </Text>
              <HStack spacing={4}>
                <HStack>
                  <Icon as={FaEnvelope} color={secondaryTextColor} />
                  {editingField === "email" ? (
                    <Input
                      value={editedValues.email || user?.email || ""}
                      onChange={(e) =>
                        handleInputChange("email", e.target.value)
                      }
                      onKeyDown={(e) => {
                        if (e.key === "Enter") {
                          handleSaveField();
                        } else if (e.key === "Escape") {
                          handleCancelEdit();
                        }
                      }}
                      autoFocus
                      size="sm"
                      width="200px"
                    />
                  ) : (
                    <Text
                      fontSize="sm"
                      color={secondaryTextColor}
                      onClick={() => handleFieldClick("email")}
                      cursor="pointer"
                      _hover={{ color: "blue.500" }}
                    >
                      {editedValues.email || user?.email || "N/A"}
                    </Text>
                  )}
                </HStack>
                <HStack>
                  <Icon as={FaPhone} color={secondaryTextColor} />
                  {editingField === "phone" ? (
                    <Input
                      value={editedValues.phone || user?.phone || ""}
                      onChange={(e) =>
                        handleInputChange("phone", e.target.value)
                      }
                      onKeyDown={(e) => {
                        if (e.key === "Enter") {
                          handleSaveField();
                        } else if (e.key === "Escape") {
                          handleCancelEdit();
                        }
                      }}
                      autoFocus
                      size="sm"
                      width="150px"
                    />
                  ) : (
                    <Text
                      fontSize="sm"
                      color={secondaryTextColor}
                      onClick={() => handleFieldClick("phone")}
                      cursor="pointer"
                      _hover={{ color: "blue.500" }}
                    >
                      {editedValues.phone || user?.phone || "N/A"}
                    </Text>
                  )}
                </HStack>
              </HStack>
            </VStack>

            <HStack mt={{ base: 4, md: 0 }} spacing={3}>
              {hasChanges && (
                <Button colorScheme="blue" size="sm" onClick={handleSave}>
                  Save Changes
                </Button>
              )}
              <Button
                as={Link}
                to="/employees"
                variant="outline"
                colorScheme="blue"
                size="sm"
              >
                View Team
              </Button>
            </HStack>
          </Flex>
        </Box>

        {/* Profile Details */}
        <Box
          bg={cardBg}
          borderRadius="lg"
          boxShadow="md"
          p={6}
          borderWidth={1}
          borderColor={borderColor}
        >
          <Text fontSize="xl" fontWeight="semibold" color={textColor} mb={4}>
            Personal Information
          </Text>
          <Divider mb={4} />

          <VStack align="start" spacing={4}>
            <HStack w="full" justify="space-between">
              <HStack>
                <Icon as={FaUser} color={secondaryTextColor} />
                <Text fontSize="md" color={textColor} fontWeight="medium">
                  First Name:
                </Text>
              </HStack>
              {editingField === "firstname" ? (
                <Input
                  value={editedValues.firstname || user?.firstname || ""}
                  onChange={(e) =>
                    handleInputChange("firstname", e.target.value)
                  }
                  onKeyDown={(e) => {
                    if (e.key === "Enter") {
                      handleSaveField();
                    } else if (e.key === "Escape") {
                      handleCancelEdit();
                    }
                  }}
                  autoFocus
                  size="sm"
                  width="200px"
                />
              ) : (
                <Text
                  fontSize="md"
                  color={secondaryTextColor}
                  onClick={() => handleFieldClick("firstname")}
                  cursor="pointer"
                  _hover={{ color: "blue.500" }}
                >
                  {editedValues.firstname || user?.firstname || "N/A"}
                </Text>
              )}
            </HStack>

            <HStack w="full" justify="space-between">
              <HStack>
                <Icon as={FaUser} color={secondaryTextColor} />
                <Text fontSize="md" color={textColor} fontWeight="medium">
                  Middle Name:
                </Text>
              </HStack>
              {editingField === "middlename" ? (
                <Input
                  value={editedValues.middlename || user?.middlename || ""}
                  onChange={(e) =>
                    handleInputChange("middlename", e.target.value)
                  }
                  onKeyDown={(e) => {
                    if (e.key === "Enter") {
                      handleSaveField();
                    } else if (e.key === "Escape") {
                      handleCancelEdit();
                    }
                  }}
                  autoFocus
                  size="sm"
                  width="200px"
                />
              ) : (
                <Text
                  fontSize="md"
                  color={secondaryTextColor}
                  onClick={() => handleFieldClick("middlename")}
                  cursor="pointer"
                  _hover={{ color: "blue.500" }}
                >
                  {editedValues.middlename || user?.middlename || "N/A"}
                </Text>
              )}
            </HStack>

            <HStack w="full" justify="space-between">
              <HStack>
                <Icon as={FaUser} color={secondaryTextColor} />
                <Text fontSize="md" color={textColor} fontWeight="medium">
                  Last Name:
                </Text>
              </HStack>
              {editingField === "lastname" ? (
                <Input
                  value={editedValues.lastname || user?.lastname || ""}
                  onChange={(e) =>
                    handleInputChange("lastname", e.target.value)
                  }
                  onKeyDown={(e) => {
                    if (e.key === "Enter") {
                      handleSaveField();
                    } else if (e.key === "Escape") {
                      handleCancelEdit();
                    }
                  }}
                  autoFocus
                  size="sm"
                  width="200px"
                />
              ) : (
                <Text
                  fontSize="md"
                  color={secondaryTextColor}
                  onClick={() => handleFieldClick("lastname")}
                  cursor="pointer"
                  _hover={{ color: "blue.500" }}
                >
                  {editedValues.lastname || user?.lastname || "N/A"}
                </Text>
              )}
            </HStack>

            <HStack w="full" justify="space-between">
              <HStack>
                <Icon as={FaPhone} color={secondaryTextColor} />
                <Text fontSize="md" color={textColor} fontWeight="medium">
                  Phone:
                </Text>
              </HStack>
              {editingField === "phone" ? (
                <Input
                  value={editedValues.phone || user?.phone || ""}
                  onChange={(e) => handleInputChange("phone", e.target.value)}
                  onKeyDown={(e) => {
                    if (e.key === "Enter") {
                      handleSaveField();
                    } else if (e.key === "Escape") {
                      handleCancelEdit();
                    }
                  }}
                  autoFocus
                  size="sm"
                  width="200px"
                />
              ) : (
                <Text
                  fontSize="md"
                  color={secondaryTextColor}
                  onClick={() => handleFieldClick("phone")}
                  cursor="pointer"
                  _hover={{ color: "blue.500" }}
                >
                  {editedValues.phone || user?.phone || "N/A"}
                </Text>
              )}
            </HStack>

            <HStack w="full" justify="space-between">
              <HStack>
                <Icon as={FaEnvelope} color={secondaryTextColor} />
                <Text fontSize="md" color={textColor} fontWeight="medium">
                  Email:
                </Text>
              </HStack>
              {editingField === "email" ? (
                <Input
                  value={editedValues.email || user?.email || ""}
                  onChange={(e) => handleInputChange("email", e.target.value)}
                  onKeyDown={(e) => {
                    if (e.key === "Enter") {
                      handleSaveField();
                    } else if (e.key === "Escape") {
                      handleCancelEdit();
                    }
                  }}
                  autoFocus
                  size="sm"
                  width="200px"
                />
              ) : (
                <Text
                  fontSize="md"
                  color={secondaryTextColor}
                  onClick={() => handleFieldClick("email")}
                  cursor="pointer"
                  _hover={{ color: "blue.500" }}
                >
                  {editedValues.email || user?.email || "N/A"}
                </Text>
              )}
            </HStack>

            <HStack w="full" justify="space-between">
              <HStack>
                <Icon as={FaUser} color={secondaryTextColor} />
                <Text fontSize="md" color={textColor} fontWeight="medium">
                  Role:
                </Text>
              </HStack>
              <Text fontSize="md" color={secondaryTextColor}>
                {user?.role?.name || "User"}
              </Text>
            </HStack>
          </VStack>
        </Box>

        {/* Password Change Card */}
        <Box
          bg={cardBg}
          borderRadius="lg"
          boxShadow="md"
          p={6}
          mt={6}
          borderWidth={1}
          borderColor={borderColor}
        >
          <Text fontSize="xl" fontWeight="semibold" color={textColor} mb={4}>
            Change Password
          </Text>
          <Divider mb={4} />

          <VStack spacing={4} align="stretch">
            {/* Current Password */}
            <HStack w="full" justify="space-between">
              <HStack>
                <Icon as={FaLock} color={secondaryTextColor} />
                <Text fontSize="md" color={textColor} fontWeight="medium">
                  Current Password:
                </Text>
              </HStack>
              <Flex position="relative" width="200px">
                <Input
                  type={showCurrentPassword ? "text" : "password"}
                  value={currentPassword}
                  onChange={(e) => setCurrentPassword(e.target.value)}
                  placeholder="Enter current password"
                  size="sm"
                  pr="2.5rem"
                />
                <Button
                  position="absolute"
                  right="0"
                  top="0"
                  h="100%"
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                >
                  <Icon as={showCurrentPassword ? FaEyeSlash : FaEye} />
                </Button>
              </Flex>
            </HStack>

            {/* New Password */}
            <HStack w="full" justify="space-between">
              <HStack>
                <Icon as={FaLock} color={secondaryTextColor} />
                <Text fontSize="md" color={textColor} fontWeight="medium">
                  New Password:
                </Text>
              </HStack>
              <Flex position="relative" width="200px">
                <Input
                  type={showNewPassword ? "text" : "password"}
                  value={newPassword}
                  onChange={(e) => {
                    setNewPassword(e.target.value);
                    if (confirmPassword && e.target.value !== confirmPassword) {
                      setPasswordError("Passwords do not match");
                    } else {
                      setPasswordError(null);
                    }
                  }}
                  placeholder="Enter new password"
                  size="sm"
                  pr="2.5rem"
                />
                <Button
                  position="absolute"
                  right="0"
                  top="0"
                  h="100%"
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowNewPassword(!showNewPassword)}
                >
                  <Icon as={showNewPassword ? FaEyeSlash : FaEye} />
                </Button>
              </Flex>
            </HStack>

            {/* Confirm New Password */}
            <HStack w="full" justify="space-between">
              <HStack>
                <Icon as={FaLock} color={secondaryTextColor} />
                <Text fontSize="md" color={textColor} fontWeight="medium">
                  Confirm Password:
                </Text>
              </HStack>
              <Flex position="relative" width="200px">
                <Input
                  type={showConfirmPassword ? "text" : "password"}
                  value={confirmPassword}
                  onChange={(e) => {
                    setConfirmPassword(e.target.value);
                    if (newPassword && e.target.value !== newPassword) {
                      setPasswordError("Passwords do not match");
                    } else {
                      setPasswordError(null);
                    }
                  }}
                  placeholder="Confirm new password"
                  size="sm"
                  pr="2.5rem"
                />
                <Button
                  position="absolute"
                  right="0"
                  top="0"
                  h="100%"
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                >
                  <Icon as={showConfirmPassword ? FaEyeSlash : FaEye} />
                </Button>
              </Flex>
            </HStack>

            {/* Error message */}
            {passwordError && (
              <Text color="red.500" fontSize="sm">
                {passwordError}
              </Text>
            )}

            {/* Submit button */}
            <Flex justify="flex-end">
              <Button
                colorScheme="blue"
                size="sm"
                onClick={async () => {
                  // Validate inputs
                  if (!currentPassword) {
                    setPasswordError("Current password is required");
                    return;
                  }
                  if (!newPassword) {
                    setPasswordError("New password is required");
                    return;
                  }
                  if (newPassword !== confirmPassword) {
                    setPasswordError("Passwords do not match");
                    return;
                  }

                  try {
                    const { data, error } = await changePassword({
                      currentPassword,
                      newPassword,
                    });

                    if (error || !data?.changePassword.status) {
                      const errorMessage =
                        data?.changePassword.error?.message ||
                        "Failed to change password";
                      setPasswordError(errorMessage);
                      toast({
                        title: "Error",
                        description: errorMessage,
                        status: "error",
                        isClosable: true,
                      });
                      return;
                    }

                    // Success
                    toast({
                      title: "Success",
                      description: "Password changed successfully",
                      status: "success",
                      isClosable: true,
                    });

                    // Reset form
                    setCurrentPassword("");
                    setNewPassword("");
                    setConfirmPassword("");
                    setPasswordError(null);
                  } catch (err) {
                    console.error("Error changing password:", err);
                    setPasswordError("An unexpected error occurred");
                    toast({
                      title: "Error",
                      description: "An unexpected error occurred",
                      status: "error",
                      isClosable: true,
                    });
                  }
                }}
                isDisabled={
                  !currentPassword ||
                  !newPassword ||
                  !confirmPassword ||
                  newPassword !== confirmPassword
                }
              >
                Change Password
              </Button>
            </Flex>
          </VStack>
        </Box>
      </Box>
    </Flex>
  );
};

export default UserProfile;
