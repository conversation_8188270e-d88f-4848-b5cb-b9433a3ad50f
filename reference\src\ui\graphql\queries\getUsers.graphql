query getUsers($roles: [Float!]) {
  getUsers(roles: $roles) {
    id
    firstname
    middlename
    lastname
    email
    phone
    companyId
    company {
      id
      name
    }
    role {
      id
      name
    }
    employee {
      id
      status
      designation
      licenceNumber
      role {
        id
        name
      }
      department {
        id
        name
      }
    }
  }
}

query getUser($id: Float!) {
  getUser(id: $id) {
    id
    firstname
    middlename
    lastname
    email
    phone
    image
    companyId
    company {
      id
      name
    }
    role {
      id
      name
    }
    employee {
      id
      status
      designation
      licenceNumber
      role {
        id
        name
      }
      department {
        id
        name
      }
    }
    permissions {
      id
      name
    }
  }
}
