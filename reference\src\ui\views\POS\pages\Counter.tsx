/* eslint-disable @typescript-eslint/no-explicit-any */
import { useContext, useEffect, useState } from "react";
import { useHistory, useLocation } from "react-router-dom";
import { FaStore } from "react-icons/fa";
// Chakra imports
import {
  Flex,
  Icon,
  Table,
  Tbody,
  Text,
  Th,
  Thead,
  Tr,
  Box,
  Grid,
  GridItem,
  useColorModeValue,
  useToast,
} from "@chakra-ui/react";
import { MeContext } from "../../../components/Wrapper";
import {
  Item,
  Store,
  useDeleteItemMutation,
  useGetStoreItemsQuery,
} from "../../../generated/graphql";
import DeleteConfirm from "../../../components/DeleteConfirmation";
import ProductRow from "../components/ProductRow";
// Custom components

function Counter() {
  const me = useContext(MeContext);
  const history = useHistory();
  const location = useLocation<{ counter: Store }>();
  useEffect(() => {
    if (!location.state || location.state.counter === undefined)
      return history.push("counters");
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  const { counter } = location.state;
  const [{ data, fetching }, getStoreItemsAsync] = useGetStoreItemsQuery({
    variables: {
      storeId: counter.id,
    },
    requestPolicy: "network-only",
  });
  const [openDelete, setopenDelete] = useState({ open: false, id: -1000000 });
  const [{ fetching: loadingDelete }, deleteItem] = useDeleteItemMutation();

  const textColor = useColorModeValue("gray.700", "white");
  const bgColor = useColorModeValue("white", "gray.700");
  const borderColor = useColorModeValue("gray.200", "gray.600");
  const toast = useToast({
    position: "top",
  });

  const callDeleteItem: any = async (id: number) => {
    console.log("delete with id: ", id);

    const { error } = await deleteItem({ id });
    setopenDelete({ open: false, id: -1000000 });
    if (error)
      toast({
        title: "Item delete failed!",
        variant: "left-accent",
        status: "error",
        isClosable: true,
      });
    else {
      await getStoreItemsAsync({ requestPolicy: "network-only" });
      toast({
        title: "Item deleted successful!",
        variant: "left-accent",
        status: "success",
        isClosable: true,
      });
    }
  };

  return (
    <Flex
      direction="column"
      pt={{ base: "60px", md: "25px" }}
      flex="1" // Take remaining vertical space
      overflowY="auto"
      maxHeight={{ base: "calc(100vh - 0px)", md: "calc(100vh - 0px)" }}
      sx={{
        "&::-webkit-scrollbar": {
          width: "1px",
        },
        "&::-webkit-scrollbar-thumb": {
          background: "gray.300",
          borderRadius: "4px",
        },
        "&::-webkit-scrollbar-thumb:hover": {
          background: "gray.500",
        },
      }}
    >
      <DeleteConfirm
        loading={loadingDelete}
        item="Item"
        open={openDelete.open}
        id={openDelete.id!}
        feedback={callDeleteItem}
        nofeedback={() => setopenDelete({ open: false, id: -1000000 })}
      />

      <Flex direction="column" pt={{ base: "120px", md: "75px" }}>
        <Box
          p={5}
          bg={bgColor}
          borderRadius="lg"
          boxShadow="md"
          border="1px solid"
          borderColor={borderColor}
          mb={5}
        >
          <Grid templateColumns="repeat(3, 1fr)" gap={6}>
            <GridItem>
              <Text fontSize="2xl" fontWeight="bold" color={textColor}>
                <Icon as={FaStore} mr={2} />
                Counter Details
              </Text>
            </GridItem>
            <GridItem>
              <Text fontSize="lg" fontWeight="bold" color={textColor}>
                <Box as="span" minWidth="120px" display="inline-block">
                  Name:
                </Box>
                {counter.name}
              </Text>
              <Text fontSize="md" color={textColor}>
                <Box as="span" minWidth="120px" display="inline-block">
                  Location:
                </Box>
                {counter.address}
              </Text>
              <Text fontSize="md" color={textColor}>
                <Box as="span" minWidth="120px" display="inline-block">
                  Primary Counter:
                </Box>
                {counter.primary ? "Yes" : "No"}
              </Text>
            </GridItem>
          </Grid>
        </Box>
      </Flex>
      <Table variant="striped" color={textColor}>
        <Thead>
          <Tr my=".8rem" pl="0px" color="black">
            <Th pl="0px" borderColor={borderColor}>
              Trade Name
            </Th>
            <Th pl="0px" borderColor={borderColor}>
              Generic Name
            </Th>
            <Th borderColor={borderColor}>Product type</Th>
            <Th borderColor={borderColor}>Stock</Th>
            <Th borderColor={borderColor}></Th>
          </Tr>
        </Thead>
        <Tbody>
          {!fetching &&
            data?.getStoreItems &&
            data?.getStoreItems.map((item, index, arr) => {
              console.log("counter items: ", item);
              return (
                <ProductRow
                  callOnClick={() => setopenDelete({ open: true, id: item.id })}
                  role={me?.role!.name ? me?.role!.name : "Guest"}
                  history={history}
                  isLast={index === arr.length - 1 ? true : false}
                  key={item.id}
                  item={item as Item}
                />
              );
            })}
        </Tbody>
      </Table>
    </Flex>
  );
}

export default Counter;
