mutation forgotPassword($email: String!) {
  forgotPassword(email: $email) {
    ...BooleanResponse
  }
}
mutation Login($params: EmailPasswordArgs!) {
  login(params: $params) {
    user {
      id
      role {
        id
        name
      }
      company {
        id
      }
    }
    token
    error {
      ...Error
    }
  }
}

mutation Logout {
  logout
}
mutation resetPassword($token: String!, $newPassword: String!) {
  resetPassword(token: $token, newPassword: $newPassword) {
    user {
      ...Me
    }
    error {
      ...Error
    }
  }
}

mutation register($params: RegisterUserArgs!) {
  register(params: $params) {
    status
    error {
      ...Error
    }
  }
}

mutation editUser($id: Float!, $params: EditUserArgs!) {
  editUser(id: $id, params: $params) {
    status
    error {
      target
      message
    }
  }
}

mutation changeEmployeeStatus($employeeId: Float!, $status: String!) {
  changeEmployeeStatus(employeeId: $employeeId, status: $status) {
    ...BooleanResponse
  }
}

mutation changeEmployeeRole(
  $employeeId: Float!
  $companyRole: Float!
  $departmentId: Float!
  $designation: String!
) {
  changeEmployeeRole(
    employeeId: $employeeId
    companyRole: $companyRole
    departmentId: $departmentId
    designation: $designation
  ) {
    ...BooleanResponse
  }
}

mutation changePassword($currentPassword: String!, $newPassword: String!) {
  changePassword(currentPassword: $currentPassword, newPassword: $newPassword) {
    ...BooleanResponse
  }
}
