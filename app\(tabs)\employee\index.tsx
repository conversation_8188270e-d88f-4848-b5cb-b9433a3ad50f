import React, { useState, useEffect, useContext, useRef } from "react";
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  TextInput,
  Image,
  SafeAreaView,
  KeyboardAvoidingView,
  Platform,
  Modal,
  ScrollView,
} from "react-native";
import { useTheme } from "react-native-paper";
import { Ionicons, MaterialCommunityIcons } from "@expo/vector-icons";
import { useRouter } from "expo-router";
import * as Haptics from "expo-haptics";
import { ActivityIndicator } from "react-native-paper";
import { useMeContext } from "@/contexts/MeContext";
import {
  Item,
  useGetCategoriesQuery,
  useGetMerchandiseItemsQuery,
  useLogoutMutation,
  useQuickSaleMutation,
} from "@/generated/graphql";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { CameraView, Camera } from "expo-camera";

// Types
interface CartItem extends Item {
  quantity: number;
  unit: string;
  price: number;
}

const MobilePOS = () => {
  const { me } = useMeContext();
  const router = useRouter();
  const theme = useTheme();
  const [logout] = useLogoutMutation();

  // Only allow employees
  useEffect(() => {
    if (!me || me.role.name !== "employee") {
      router.replace("/login");
      logout();
    }
  }, [me]);

  // State
  const [search, setSearch] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("");
  const [cart, setCart] = useState<CartItem[]>([]);
  const [cartVisible, setCartVisible] = useState(false);
  const [selectedTable, setSelectedTable] = useState("");
  const [showTableModal, setShowTableModal] = useState(false);
  const [checkoutLoading, setCheckoutLoading] = useState(false);
  const [checkoutError, setCheckoutError] = useState("");
  const [unitModal, setUnitModal] = useState({
    visible: false,
    product: null,
  } as { visible: boolean; product: Item | null });
  const [selectedUnit, setSelectedUnit] = useState(null);
  const [quickSale] = useQuickSaleMutation();
  const [barcodeModal, setBarcodeModal] = useState(false);
  const [hasPermission, setHasPermission] = useState<boolean | null>(null);

  // Data
  const { data: merchandiseData } = useGetMerchandiseItemsQuery();
  const { data: categoryData } = useGetCategoriesQuery({
    variables: { type: "Item_Category" },
    fetchPolicy: "network-only",
  });
  const { data: customerTagData } = useGetCategoriesQuery({
    variables: { type: "Customer_Tag" },
    fetchPolicy: "network-only",
  });

  const products = merchandiseData?.getMerchandiseItems || [];
  const categories = categoryData?.getCategories || [];
  const customerTags = customerTagData?.getCategories || [];

  // Filtering
  const filteredProducts = products.filter((product) => {
    const matchesCategory = selectedCategory
      ? product.reference === selectedCategory
      : true;
    const matchesSearch = search
      ? product.name.toLowerCase().includes(search.toLowerCase()) ||
        product.barcode?.toLowerCase().includes(search.toLowerCase())
      : true;
    return matchesCategory && matchesSearch;
  });

  // Restore cart from AsyncStorage on mount
  useEffect(() => {
    (async () => {
      const stored = await AsyncStorage.getItem("pos_cart");
      if (stored) setCart(JSON.parse(stored));
    })();
  }, []);

  // Persist cart to AsyncStorage on change
  useEffect(() => {
    AsyncStorage.setItem("pos_cart", JSON.stringify(cart));
  }, [cart]);

  // Barcode scanner permission
  useEffect(() => {
    if (barcodeModal && hasPermission === null) {
      (async () => {
        const { status } = await Camera.requestCameraPermissionsAsync();
        setHasPermission(status === "granted");
      })();
    }
  }, [barcodeModal]);

  // Barcode scan handler
  const handleBarCodeScanned = ({ data }: { data: string }) => {
    setBarcodeModal(false);
    setSearch(data);
    // Optionally auto-add if only one match
    const match = filteredProducts.find((p) => p.barcode === data);
    if (match) addToCart(match as Item);
  };

  // Cart logic
  const addToCart = (product: Item) => {
    // Stock validation
    const inCart = cart.find((item) => item.id === product.id);
    if (product.stock === 0 || (inCart && inCart.quantity >= product.stock)) {
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
      return;
    }
    // Unit selection if multiple units
    if (product.units && product.units.length > 1) {
      setUnitModal({ visible: true, product });
      return;
    }
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    setCart((prev) => {
      const existing = prev.find((item) => item.id === product.id);
      if (existing) {
        return prev.map((item) =>
          item.id === product.id
            ? { ...item, quantity: item.quantity + 1 }
            : item
        );
      } else {
        return [
          ...prev,
          {
            ...product,
            quantity: 1,
            unit: product.unit,
            price: product.sellingPrice,
          },
        ];
      }
    });
  };
  const addToCartWithUnit = (
    product: Item,
    unit: { name: string; price: number }
  ) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    setCart((prev) => {
      const existing = prev.find(
        (item) => item.id === product.id && item.unit === unit.name
      );
      if (existing) {
        return prev.map((item) =>
          item.id === product.id && item.unit === unit.name
            ? { ...item, quantity: item.quantity + 1 }
            : item
        );
      } else {
        return [
          ...prev,
          { ...product, quantity: 1, unit: unit.name, price: unit.price },
        ];
      }
    });
    setUnitModal({ visible: false, product: null });
  };
  const removeFromCart = (id: number) =>
    setCart((prev) => prev.filter((item) => item.id !== id));
  const updateQuantity = (id: number, quantity: number) =>
    setCart((prev) =>
      prev.map((item) => (item.id === id ? { ...item, quantity } : item))
    );
  const clearCart = () => setCart([]);

  // Checkout handler
  const handleCheckout = async () => {
    if (cart.length === 0) return;

    setCheckoutLoading(true);
    setCheckoutError("");

    try {
      const saleItems = cart.map((item) => ({
        itemId: item.id,
        quantity: item.quantity,
        unit: item.unit,
        remarks: selectedTable || "Walk-in Customer",
      }));

      await quickSale({
        variables: {
          args: saleItems,
        },
      });

      // Success - clear cart and close modal
      clearCart();
      setCartVisible(false);
      setSelectedTable("");

      // Show success feedback
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
    } catch (error) {
      console.error("Checkout error:", error);
      setCheckoutError("Failed to process sale. Please try again.");
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
    } finally {
      setCheckoutLoading(false);
    }
  };

  // UI
  return (
    <SafeAreaView style={styles.safeArea}>
      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : undefined}
        style={{ flex: 1 }}
      >
        <View style={styles.header}>
          <Text style={styles.title}>Point of Sale</Text>
          <TouchableOpacity
            onPress={() => setCartVisible(true)}
            style={styles.cartButton}
          >
            <Ionicons
              name="cart-outline"
              size={28}
              color={theme.colors.primary}
            />
            {cart.length > 0 && (
              <View style={styles.cartBadge}>
                <Text style={styles.cartBadgeText}>{cart.length}</Text>
              </View>
            )}
          </TouchableOpacity>
        </View>
        <View style={styles.searchRow}>
          <View style={styles.searchBox}>
            <Ionicons
              name="search"
              size={20}
              color="#888"
              style={{ marginRight: 8 }}
            />
            <TextInput
              style={styles.searchInput}
              placeholder="Search products or scan barcode..."
              value={search}
              onChangeText={setSearch}
              placeholderTextColor="#aaa"
            />
            <TouchableOpacity
              onPress={() => setBarcodeModal(true)}
              style={{ marginLeft: 8 }}
            >
              <Ionicons
                name="barcode-outline"
                size={24}
                color={theme.colors.primary}
              />
            </TouchableOpacity>
          </View>
          <TouchableOpacity
            style={styles.tableButton}
            onPress={() => setShowTableModal(true)}
          >
            <MaterialCommunityIcons
              name="table-chair"
              size={24}
              color={theme.colors.primary}
            />
            <Text style={styles.tableButtonText}>
              {selectedTable || "Table/Tag"}
            </Text>
          </TouchableOpacity>
        </View>
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          style={styles.categoryScroll}
        >
          <TouchableOpacity
            onPress={() => setSelectedCategory("")}
            style={[
              styles.categoryBadge,
              !selectedCategory && styles.categoryBadgeActive,
            ]}
          >
            <Text
              style={[
                styles.categoryText,
                !selectedCategory && styles.categoryTextActive,
              ]}
            >
              All
            </Text>
          </TouchableOpacity>
          {categories.map((cat) => (
            <TouchableOpacity
              key={cat.id}
              onPress={() => setSelectedCategory(cat.name)}
              style={[
                styles.categoryBadge,
                selectedCategory === cat.name && styles.categoryBadgeActive,
              ]}
            >
              <Text
                style={[
                  styles.categoryText,
                  selectedCategory === cat.name && styles.categoryTextActive,
                ]}
              >
                {cat.name}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
        <FlatList
          data={filteredProducts}
          keyExtractor={(item) => item.id.toString()}
          numColumns={2}
          contentContainerStyle={styles.productGrid}
          renderItem={({ item }) => (
            <TouchableOpacity
              style={styles.productCard}
              onPress={() => addToCart(item as Item)}
              disabled={item.stock === 0}
            >
              <Image source={{ uri: item.image }} style={styles.productImage} />
              <Text style={styles.productName}>{item.name}</Text>
              <Text style={styles.productStock}>Stock: {item.stock}</Text>
              <Text style={styles.productPrice}>{item.sellingPrice} Tsh</Text>
              <View style={styles.addToCartRow}>
                <Ionicons
                  name="add-circle"
                  size={24}
                  color={item.stock === 0 ? "#ccc" : theme.colors.primary}
                />
                <Text style={styles.addToCartText}>
                  {item.stock === 0 ? "Out of Stock" : "Add to Cart"}
                </Text>
              </View>
            </TouchableOpacity>
          )}
        />
        {/* Cart Modal/Bottom Sheet */}
        <Modal visible={cartVisible} animationType="slide" transparent>
          <View style={styles.cartModalOverlay}>
            <View style={styles.cartModal}>
              <View style={styles.cartHeader}>
                <Text style={styles.cartTitle}>Cart</Text>
                <TouchableOpacity onPress={() => setCartVisible(false)}>
                  <Ionicons
                    name="close"
                    size={28}
                    color={theme.colors.primary}
                  />
                </TouchableOpacity>
              </View>
              <ScrollView style={styles.cartList}>
                {cart.length === 0 ? (
                  <Text style={styles.emptyCartText}>Your cart is empty.</Text>
                ) : (
                  cart.map((item) => (
                    <View key={item.id} style={styles.cartItem}>
                      <Text style={styles.cartItemName}>{item.name}</Text>
                      <View style={styles.cartItemRow}>
                        <TouchableOpacity
                          onPress={() =>
                            updateQuantity(
                              item.id,
                              Math.max(1, item.quantity - 1)
                            )
                          }
                        >
                          <Ionicons
                            name="remove-circle-outline"
                            size={24}
                            color={theme.colors.primary}
                          />
                        </TouchableOpacity>
                        <Text style={styles.cartItemQty}>{item.quantity}</Text>
                        <TouchableOpacity
                          onPress={() =>
                            updateQuantity(item.id, item.quantity + 1)
                          }
                        >
                          <Ionicons
                            name="add-circle-outline"
                            size={24}
                            color={theme.colors.primary}
                          />
                        </TouchableOpacity>
                        <Text style={styles.cartItemPrice}>
                          {item.price * item.quantity} Tsh
                        </Text>
                        <TouchableOpacity
                          onPress={() => removeFromCart(item.id)}
                        >
                          <Ionicons
                            name="trash-outline"
                            size={22}
                            color="#EF4444"
                          />
                        </TouchableOpacity>
                      </View>
                    </View>
                  ))
                )}
              </ScrollView>
              {checkoutError ? (
                <Text style={styles.errorText}>{checkoutError}</Text>
              ) : null}
              <View style={styles.cartFooter}>
                <TouchableOpacity
                  style={styles.clearCartButton}
                  onPress={clearCart}
                >
                  <Text style={styles.clearCartText}>Clear Cart</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[
                    styles.checkoutButton,
                    checkoutLoading && { opacity: 0.7 },
                  ]}
                  onPress={handleCheckout}
                  disabled={checkoutLoading || cart.length === 0}
                >
                  {checkoutLoading ? (
                    <ActivityIndicator color="#fff" size="small" />
                  ) : (
                    <Text style={styles.checkoutText}>
                      Checkout (
                      {cart.reduce(
                        (sum, item) => sum + item.price * item.quantity,
                        0
                      )}{" "}
                      Tsh)
                    </Text>
                  )}
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </Modal>
        {/* Table/Tag Modal */}
        <Modal visible={showTableModal} animationType="fade" transparent>
          <View style={styles.tableModalOverlay}>
            <View style={styles.tableModal}>
              <Text style={styles.tableModalTitle}>
                Select or Create Table/Tag
              </Text>
              <ScrollView style={styles.tableList}>
                {customerTags.map((tag) => (
                  <TouchableOpacity
                    key={tag.id}
                    style={styles.tableItem}
                    onPress={() => {
                      setSelectedTable(tag.name);
                      setShowTableModal(false);
                    }}
                  >
                    <Text style={styles.tableItemText}>{tag.name}</Text>
                  </TouchableOpacity>
                ))}
              </ScrollView>
              <TextInput
                style={styles.tableInput}
                placeholder="Create new tag..."
                value={selectedTable}
                onChangeText={setSelectedTable}
                placeholderTextColor="#aaa"
              />
              <TouchableOpacity
                style={styles.createTableButton}
                onPress={() => setShowTableModal(false)}
              >
                <Text style={styles.createTableText}>Done</Text>
              </TouchableOpacity>
            </View>
          </View>
        </Modal>
        {/* Barcode Scanner Modal */}
        <Modal visible={barcodeModal} animationType="slide" transparent>
          <View
            style={{
              flex: 1,
              backgroundColor: "rgba(0,0,0,0.95)",
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            {hasPermission === null ? (
              <ActivityIndicator color="#fff" />
            ) : hasPermission === false ? (
              <Text style={{ color: "#fff" }}>No access to camera</Text>
            ) : (
              <CameraView
                style={{ width: 320, height: 320 }}
                barcodeScannerSettings={{
                  barcodeTypes: [
                    "qr",
                    "pdf417",
                    "ean13",
                    "ean8",
                    "code128",
                    "code39",
                  ],
                }}
                onBarcodeScanned={handleBarCodeScanned}
              />
            )}
            <TouchableOpacity
              onPress={() => setBarcodeModal(false)}
              style={{ marginTop: 24 }}
            >
              <Text
                style={{ color: "#3B82F6", fontWeight: "bold", fontSize: 18 }}
              >
                Cancel
              </Text>
            </TouchableOpacity>
          </View>
        </Modal>
        {/* Unit Selection Modal */}
        <Modal visible={unitModal.visible} animationType="fade" transparent>
          <View style={styles.tableModalOverlay}>
            <View style={styles.tableModal}>
              <Text style={styles.tableModalTitle}>Select Unit</Text>
              <ScrollView style={styles.tableList}>
                {unitModal.product?.units?.map((unit, index) => (
                  <TouchableOpacity
                    key={index}
                    style={styles.tableItem}
                    onPress={() => addToCartWithUnit(unitModal.product!, unit)}
                  >
                    <Text style={styles.tableItemText}>
                      {unit.name} - {unit.price} Tsh
                    </Text>
                  </TouchableOpacity>
                ))}
              </ScrollView>
              <TouchableOpacity
                style={styles.clearCartButton}
                onPress={() => setUnitModal({ visible: false, product: null })}
              >
                <Text style={styles.clearCartText}>Cancel</Text>
              </TouchableOpacity>
            </View>
          </View>
        </Modal>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeArea: { flex: 1, backgroundColor: "#0f172a" },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    padding: 20,
    backgroundColor: "#18181b",
  },
  title: { fontSize: 24, fontWeight: "bold", color: "#fff", letterSpacing: 1 },
  cartButton: { position: "relative", padding: 8 },
  cartBadge: {
    position: "absolute",
    top: 2,
    right: 2,
    backgroundColor: "#3B82F6",
    borderRadius: 10,
    paddingHorizontal: 6,
    paddingVertical: 2,
  },
  cartBadgeText: { color: "#fff", fontWeight: "bold", fontSize: 12 },
  searchRow: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 20,
    marginTop: 10,
  },
  searchBox: {
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#23272f",
    borderRadius: 12,
    paddingHorizontal: 12,
    marginRight: 10,
  },
  searchInput: { flex: 1, color: "#fff", fontSize: 16, paddingVertical: 10 },
  tableButton: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#23272f",
    borderRadius: 12,
    paddingHorizontal: 12,
    paddingVertical: 8,
  },
  tableButtonText: { color: "#3B82F6", marginLeft: 6, fontWeight: "bold" },
  categoryScroll: { marginTop: 18, paddingHorizontal: 10, maxHeight: 48 },
  categoryBadge: {
    backgroundColor: "#23272f",
    borderRadius: 16,
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginRight: 8,
  },
  categoryBadgeActive: { backgroundColor: "#3B82F6" },
  categoryText: { color: "#fff", fontWeight: "500" },
  categoryTextActive: { color: "#fff", fontWeight: "bold" },
  productGrid: { paddingHorizontal: 10, paddingBottom: 120 },
  productCard: {
    flex: 1,
    backgroundColor: "#18181b",
    borderRadius: 18,
    margin: 8,
    padding: 16,
    alignItems: "center",
    shadowColor: "#000",
    shadowOpacity: 0.1,
    shadowRadius: 8,
    shadowOffset: { width: 0, height: 2 },
  },
  productImage: {
    width: 80,
    height: 80,
    borderRadius: 12,
    marginBottom: 8,
    backgroundColor: "#23272f",
  },
  productName: {
    color: "#fff",
    fontWeight: "bold",
    fontSize: 16,
    marginBottom: 2,
  },
  productStock: { color: "#94a3b8", fontSize: 12, marginBottom: 2 },
  productPrice: {
    color: "#3B82F6",
    fontWeight: "bold",
    fontSize: 16,
    marginBottom: 6,
  },
  addToCartRow: { flexDirection: "row", alignItems: "center", marginTop: 4 },
  addToCartText: { color: "#3B82F6", marginLeft: 6, fontWeight: "bold" },
  cartModalOverlay: {
    flex: 1,
    backgroundColor: "rgba(0,0,0,0.5)",
    justifyContent: "flex-end",
  },
  cartModal: {
    backgroundColor: "#fff",
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    padding: 20,
    minHeight: 320,
    maxHeight: "80%",
  },
  cartHeader: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginBottom: 10,
  },
  cartTitle: { fontSize: 20, fontWeight: "bold", color: "#18181b" },
  cartList: { maxHeight: 220 },
  emptyCartText: { color: "#888", textAlign: "center", marginTop: 40 },
  errorText: {
    color: "#EF4444",
    textAlign: "center",
    marginTop: 10,
    fontSize: 14,
  },
  cartItem: { marginBottom: 16 },
  cartItemName: { fontWeight: "bold", color: "#18181b", fontSize: 16 },
  cartItemRow: { flexDirection: "row", alignItems: "center", marginTop: 6 },
  cartItemQty: {
    marginHorizontal: 12,
    fontWeight: "bold",
    color: "#18181b",
    fontSize: 16,
  },
  cartItemPrice: {
    marginLeft: 12,
    color: "#3B82F6",
    fontWeight: "bold",
    fontSize: 16,
  },
  cartFooter: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginTop: 18,
  },
  clearCartButton: {
    backgroundColor: "#f3f4f6",
    borderRadius: 10,
    paddingVertical: 10,
    paddingHorizontal: 18,
  },
  clearCartText: { color: "#18181b", fontWeight: "bold" },
  checkoutButton: {
    backgroundColor: "#3B82F6",
    borderRadius: 10,
    paddingVertical: 10,
    paddingHorizontal: 28,
  },
  checkoutText: { color: "#fff", fontWeight: "bold", fontSize: 16 },
  tableModalOverlay: {
    flex: 1,
    backgroundColor: "rgba(0,0,0,0.5)",
    justifyContent: "center",
    alignItems: "center",
  },
  tableModal: {
    backgroundColor: "#fff",
    borderRadius: 18,
    padding: 24,
    width: "80%",
  },
  tableModalTitle: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#18181b",
    marginBottom: 10,
  },
  tableList: { maxHeight: 120, marginBottom: 10 },
  tableItem: {
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: "#eee",
  },
  tableItemText: { color: "#18181b", fontSize: 16 },
  tableInput: {
    borderWidth: 1,
    borderColor: "#3B82F6",
    borderRadius: 8,
    padding: 10,
    marginTop: 10,
    color: "#18181b",
  },
  createTableButton: {
    backgroundColor: "#3B82F6",
    borderRadius: 10,
    paddingVertical: 10,
    marginTop: 12,
  },
  createTableText: {
    color: "#fff",
    fontWeight: "bold",
    textAlign: "center",
    fontSize: 16,
  },
});

export default MobilePOS;
