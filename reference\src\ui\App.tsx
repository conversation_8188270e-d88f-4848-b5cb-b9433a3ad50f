import { ChakraProvider, ColorModeScript, Grid } from "@chakra-ui/react";
import { Route, HashRouter as Router, Switch } from "react-router-dom";
import { Provider } from "urql";
import client from "./utils/CreateUrqlClient";
import AuthRoute from "./utils/AuthRoute";
import UnAuthRoute from "./utils/UnAuthRoute";
import theme from "./theme/theme";
import Login from "./pages/login";
import Forgot_password from "./pages/forgot-password";
import Reset_password from "./pages/reset-password";
import NotFoundPage from "./pages/404";
import DesktopLayout from "./layouts/Desktop";
import SignInPage from "./pages/login_pos";
import UpdateNotification from "./components/UpdateNotification";
import SubscriptionExpiredPage from "./pages/subscription-expired";

export const App = () => (
  <Provider value={client}>
    <ChakraProvider theme={theme}>
      <ColorModeScript initialColorMode={theme.config.initialColorMode} />
      <Grid minH="100vh" m={0} p={0}>
        <UpdateNotification />
        <Router>
          <Switch>
            {/* Subscription expired page */}
            <Route
              exact
              path="/subscription-expired"
              component={SubscriptionExpiredPage}
            />

            {/* protected routes  */}
            <AuthRoute exact path="/desktop/POS" component={DesktopLayout} />
            <AuthRoute
              exact
              path="/desktop/categories"
              component={DesktopLayout}
            />
            <AuthRoute
              exact
              path="/desktop/counter"
              component={DesktopLayout}
            />
            <AuthRoute
              exact
              path="/desktop/counters"
              component={DesktopLayout}
            />
            <AuthRoute
              exact
              path="/desktop/add-counter"
              component={DesktopLayout}
            />
            <AuthRoute
              exact
              path="/desktop/edit-counter"
              component={DesktopLayout}
            />
            <AuthRoute
              exact
              path="/desktop/open-tabs"
              component={DesktopLayout}
            />
            <AuthRoute exact path="/desktop/roles" component={DesktopLayout} />
            <AuthRoute exact path="/desktop/sales" component={DesktopLayout} />
            <AuthRoute
              exact
              path="/desktop/employee"
              component={DesktopLayout}
            />
            <AuthRoute exact path="/desktop/user" component={DesktopLayout} />
            <AuthRoute
              exact
              path="/desktop/add-employee"
              component={DesktopLayout}
            />
            <AuthRoute
              exact
              path="/desktop/import-product"
              component={DesktopLayout}
            />
            <AuthRoute
              exact
              path="/desktop/transfer-stock"
              component={DesktopLayout}
            />
            <AuthRoute
              exact
              path="/desktop/update-profile"
              component={DesktopLayout}
            />
            <AuthRoute
              exact
              path="/desktop/employees"
              component={DesktopLayout}
            />
            <AuthRoute
              exact
              path="/desktop/pending-orders"
              component={DesktopLayout}
            />
            <AuthRoute
              exact
              path="/desktop/products"
              component={DesktopLayout}
            />
            <AuthRoute
              exact
              path="/desktop/product"
              component={DesktopLayout}
            />
            <AuthRoute
              exact
              path="/desktop/customer-tag"
              component={DesktopLayout}
            />
            <AuthRoute
              exact
              path="/desktop/manage-product"
              component={DesktopLayout}
            />
            <AuthRoute
              exact
              path="/desktop/troubleshoot"
              component={DesktopLayout}
            />
            <AuthRoute
              exact
              path="/desktop/expenses"
              component={DesktopLayout}
            />

            {/* Unprotected routes */}
            <UnAuthRoute exact path="/login" component={Login} user="user" />
            <UnAuthRoute
              exact
              path="/sign-in"
              component={SignInPage}
              user="user"
            />
            <UnAuthRoute
              exact
              path="/forgot-password"
              component={Forgot_password}
            />
            <UnAuthRoute
              exact
              path="/reset-password"
              component={Reset_password}
            />

            {/* Root path - redirect to login */}
            <UnAuthRoute exact path="/" component={Login} user="user" />

            {/* 404 page */}
            <Route component={NotFoundPage} />
          </Switch>
        </Router>
      </Grid>
    </ChakraProvider>
  </Provider>
);
