/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useContext, useEffect, useRef, useState } from "react";
import {
  Flex,
  Button,
  Text,
  useToast,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Badge,
  Tfoot,
  Box,
  Input,
} from "@chakra-ui/react";
import {
  Inventory,
  Item,
  Transfer,
  useClearServedBillMutation,
  useDeleteBillItemMutation,
  useGetOpenTabsQuery,
} from "../../../generated/graphql";
import { formatToMoney } from "../../../utils/Helpers";
import { useReactToPrint } from "react-to-print";
import TransferRow from "../components/BillRow";
import { ActionType } from "../../../interfaces/Types";
import { AppContext } from "../../../AppContext";
import { useHistory } from "react-router";
import { MeContext } from "../../../components/Wrapper";
import { hasPermission } from "../../../interfaces/Helpers";
import Receipt from "../components/receipt";

const OpenTabs: React.FC = () => {
  const toast = useToast({
    position: "top",
  });
  const { dispatch } = useContext(AppContext);
  const [{ data: sales }, reGetSales] = useGetOpenTabsQuery({
    requestPolicy: "network-only",
  });
  const [, clearBill] = useClearServedBillMutation();
  const [, deleteBillItem] = useDeleteBillItemMutation();

  const history = useHistory();
  const me = useContext(MeContext);

  const [selectedSale, setSelectedSale] = useState<Inventory | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [deleteStatuses, setDeleteStatuses] = useState<{
    [key: string]: "idle" | "loading" | "success" | "error";
  }>({});

  const receiptRef = useRef<HTMLDivElement>(null);

  const handlePrint = useReactToPrint({
    contentRef: receiptRef,
  });

  const deleteTransfer = async (transferId: number, inventoryId: number) => {
    setDeleteStatuses((prev) => ({
      ...prev,
      [transferId]: "loading",
    }));

    try {
      // Call the delete API or mutation here to delete the transfer.
      const deleteFeedback = await deleteBillItem({
        inventoryId: inventoryId,
        transferId: transferId,
      });
      if (deleteFeedback.error) throw new Error(deleteFeedback.error.message);
      else if (deleteFeedback.data?.deleteBillItem.error)
        throw new Error(deleteFeedback.data.deleteBillItem.error.message);
      // On success:
      setDeleteStatuses((prev) => ({
        ...prev,
        [transferId]: "success",
      }));

      // Refresh sales data or handle any other post-delete logic.
      reGetSales();
    } catch (error) {
      setDeleteStatuses((prev) => ({
        ...prev,
        [transferId]: "error",
      }));

      toast({
        title: "Error deleting transfer.",
        status: "error",
        isClosable: true,
      });
    }
  };

  // Calculate elapsed time from bill creation.
  const calculateElapsedTime = (createdAt: number): string => {
    const createdDate = new Date(createdAt); // Works with numeric timestamp in milliseconds
    const now = new Date();
    const diffMs = now.getTime() - createdDate.getTime();
    const diffHrs = Math.floor(diffMs / 3600000); // 3600000 ms = 1 hour
    const diffMins = Math.floor((diffMs % 3600000) / 60000); // 60000 ms = 1 minute
    return `${diffHrs} hrs, ${diffMins} mins`;
  };

  const handleClearBill = async (sale) => {
    setSelectedSale(sale);
    const { data } = await clearBill({ inventoryId: sale.id });
    if (data?.clearServedBill.error) {
      toast({
        title: data.clearServedBill.error.message,
        status: "error",
        isClosable: true,
      });
    } else {
      reGetSales();
      toast({
        title: "Bill cleared successfully!",
        variant: "left-accent",
        status: "success",
        isClosable: true,
      });
      handlePrint();
    }
  };

  useEffect(() => {
    const interval = setInterval(() => {
      reGetSales({
        requestPolicy: "network-only",
      });
    }, 5000);

    return () => clearInterval(interval);
  }, [reGetSales]);

  const calculateTotalSale = (sale: Inventory) => {
    return sale.transfers
      .filter((trans) => trans.dispatched)
      .reduce((total: number, transfer: Transfer) => total + transfer.price, 0);
  };

  const filteredSales = sales?.getOpenTabs
    .filter((sale) => {
      if (
        hasPermission(me?.permissions, [
          "Open Tabs",
          "Open Tabs>All",
          "Open Tabs>Edit",
          "Open Tabs>Delete",
        ])
      )
        return true;
      else return sale.consumer?.id === me?.id || sale.keeper?.id === me?.id;
    })
    .filter((sale) => {
      const matchesCustomerTag = sale.customerTag
        ?.toLowerCase()
        .includes(searchTerm.toLowerCase());
      const matchesAttending = sale.consumer
        ? `${sale.consumer.user.firstname} ${sale.consumer.user.lastname}`
            .toLowerCase()
            .includes(searchTerm.toLowerCase())
        : sale.keeper
        ? `${sale.keeper.user.firstname} ${sale.keeper.user.lastname}`
            .toLowerCase()
            .includes(searchTerm.toLowerCase())
        : false;

      const matchesItem = sale.items.some((item) =>
        item.name.toLowerCase().includes(searchTerm.toLowerCase())
      );

      return matchesCustomerTag || matchesAttending || matchesItem;
    });

  const updateBillItem = (sale: Inventory) => {
    // Prepare the cart based on the selected sale
    const cartItems = sale.transfers
      .filter((trans) => trans.dispatched)
      .map((transfer) => ({
        id: transfer.itemId,
        name:
          sale.items.find((item) => item.id === transfer.itemId)?.name ?? "",
        unit:
          sale.items.find((item) => item.id === transfer.itemId)?.unit ?? "",
        price: transfer.price,
        quantity: transfer.quantity,
        stock:
          sale.items.find((item) => item.id === transfer.itemId)?.stock ??
          transfer.quantity,
        hold: !transfer.dispatched,
      }));

    // Dispatch action to set cart_inventory and cart items in the context
    dispatch({
      type: ActionType.UPDATE_SAVED_CART_ITEMS,
      cart_inventory: sale.id,
      cart: {
        items: cartItems,
        totalAmount: cartItems.reduce(
          (total, item) => total + item.price * item.quantity,
          0
        ),
        itemCount: cartItems.length,
      },
    });

    // Navigate to the POS page
    history.push("pos");
  };

  return (
    <Flex
      direction="column"
      position="relative"
      mb="40px"
      mt="50px"
      flex="1" // Take remaining vertical space
      overflowY="auto"
      maxHeight={{ base: "calc(100vh - 0px)", md: "calc(100vh - 0px)" }}
      sx={{
        "&::-webkit-scrollbar": {
          width: "1px",
        },
        "&::-webkit-scrollbar-thumb": {
          background: "gray.300",
          borderRadius: "4px",
        },
        "&::-webkit-scrollbar-thumb:hover": {
          background: "gray.500",
        },
      }}
    >
      <Flex
        w="100%"
        mx="auto"
        justifyContent="space-between"
        mb="20px"
        pt={{ md: "0px" }}
        direction="column"
      >
        <Flex direction="column" overflow="auto">
          <Text
            fontSize="3xl"
            fontWeight="extrabold"
            textAlign="center"
            mb="12px"
            bgGradient="linear(to-r, teal.400, blue.500)"
            bgClip="text"
            textShadow="1px 1px 3px rgba(0, 0, 0, 0.2)"
            p={2}
          >
            Open Tabs
          </Text>

          <Input
            placeholder="Search by item name, customer tag, or attending employee"
            size="md"
            mb="16px"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />

          {filteredSales && filteredSales.length > 0 ? (
            filteredSales
              .filter((b) => b.bill?.amount && b.bill.amount > 0)
              .map((sale) => {
                const cleared = sale.bill?.cleared;
                const saleTime = sale.bill?.createdAt;
                return (
                  <Box
                    key={sale.id}
                    borderWidth="1px"
                    borderColor="gray.300"
                    borderRadius="md"
                    p={4}
                    mb={4}
                    boxShadow="md"
                  >
                    {/* Bill Header */}
                    <Flex justifyContent="space-between" align="center" mb={4}>
                      <Box>
                        {" "}
                        {cleared ? (
                          <Badge
                            colorScheme="green"
                            variant="solid"
                            borderRadius="full"
                            px={3}
                            py={1}
                          >
                            Bill Cleared
                          </Badge>
                        ) : (
                          <Badge
                            colorScheme="red"
                            variant="solid"
                            borderRadius="full"
                            px={3}
                            py={1}
                          >
                            Bill Not Cleared
                          </Badge>
                        )}
                        <Text fontSize="lg" fontWeight="bold">
                          {sale.consumer && sale.consumer.id > 0
                            ? `${sale.consumer.user.firstname} ${sale.consumer.user.lastname}`
                            : sale.keeper && sale.keeper.id > 0
                            ? `${sale.keeper.user.firstname} ${sale.keeper.user.lastname}`
                            : "Unknown Attendant"}
                        </Text>
                        <Text fontSize="sm" color="gray.600">
                          Customer Tag: {sale.customerTag || "N/A"}
                        </Text>
                        {saleTime && (
                          <Text fontSize="sm" color="gray.600">
                            Served for: {calculateElapsedTime(Number(saleTime))}
                          </Text>
                        )}
                      </Box>
                      {hasPermission(me?.permissions, [
                        "Open Tabs>Edit",
                        "Open Tabs>Delete",
                      ]) && (
                        <Box textAlign="left">
                          <Flex mt={2} gap={2}>
                            <Button
                              size="sm"
                              colorScheme="teal"
                              onClick={() => handleClearBill(sale)}
                              _hover={{
                                bg: "teal.600",
                                transform: "scale(1.05)",
                              }}
                              transition="all 0.3s"
                            >
                              Clear Bill
                            </Button>
                            {/* this featue is complicated, when updating bill item price could be different, no need for now */}
                            <Button
                              size="sm"
                              colorScheme="blue"
                              onClick={() => updateBillItem(sale as Inventory)}
                              _hover={{
                                bg: "blue.600",
                                transform: "scale(1.05)",
                              }}
                              transition="all 0.3s"
                            >
                              Update Bill
                            </Button>
                          </Flex>
                        </Box>
                      )}
                    </Flex>
                    {/* Bill Items Table */}
                    <Box overflowX="auto">
                      <Table variant="striped" size="sm">
                        <Thead>
                          <Tr>
                            <Th>Item Name</Th>
                            <Th>Quantity</Th>
                            <Th>Unit Price</Th>
                            <Th>Billed Amount</Th>
                            {hasPermission(me?.permissions, [
                              "Open Tabs>Edit",
                              "Open Tabs>Delete",
                            ]) && <Th>Actions</Th>}
                          </Tr>
                        </Thead>
                        <Tbody>
                          {sale.transfers
                            .filter((trans) => trans.dispatched)
                            .sort((a, b) => a.id - b.id) // Sort by transfer.id in ascending order
                            .map((transfer) => {
                              const item = sale.items.find(
                                (itm: any) => itm.id === transfer.itemId
                              );
                              return (
                                <TransferRow
                                  key={transfer.id}
                                  transfer={transfer as Transfer}
                                  item={item as Item}
                                  deleteTransfer={deleteTransfer} // Pass deleteTransfer handler
                                  deleteStatus={
                                    deleteStatuses[transfer.id] || "idle"
                                  } // Pass current delete status for this transfer
                                  setDeleteStatus={(
                                    status:
                                      | "idle"
                                      | "loading"
                                      | "success"
                                      | "error"
                                  ) => {
                                    setDeleteStatuses((prev) => ({
                                      ...prev,
                                      [transfer.id]: status,
                                    }));
                                  }} // Pass function to set delete status
                                />
                              );
                            })}
                        </Tbody>

                        <Tfoot>
                          <Tr>
                            <Th colSpan={3} fontSize="lg" fontWeight="bold">
                              Total Sale:
                            </Th>
                            <Th fontSize="lg" fontWeight="bold">
                              {formatToMoney(
                                calculateTotalSale(sale as Inventory)
                              )}
                            </Th>
                            <Th />
                          </Tr>
                        </Tfoot>
                      </Table>
                    </Box>
                  </Box>
                );
              })
          ) : (
            <Text textAlign="center" fontSize="lg" color="gray.600">
              No open tabs found.
            </Text>
          )}
        </Flex>
      </Flex>

      {selectedSale && (
        <div style={{ display: "none" }}>
          <Receipt
            ref={receiptRef}
            sale={selectedSale}
            total={calculateTotalSale(selectedSale)}
          />
        </div>
      )}
    </Flex>
  );
};

export default OpenTabs;
