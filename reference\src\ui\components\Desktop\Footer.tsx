/* eslint-disable prefer-const */
/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useContext, useEffect, useState } from "react";
import {
  Box,
  Flex,
  Text,
  Tooltip,
  IconButton,
  Button,
  useColorModeValue,
  Menu,
  useToast,
  MenuButton,
  MenuList,
  MenuItem,
  Badge,
  Spinner,
} from "@chakra-ui/react";
import {
  WarningTwoIcon,
  ViewIcon,
  CalendarIcon,
  InfoIcon,
  HamburgerIcon,
  TimeIcon,
} from "@chakra-ui/icons";
import { FaWifi, FaServer, FaExclamationTriangle } from "react-icons/fa";
import { PiWifiSlash } from "react-icons/pi";
import { Link, useHistory } from "react-router-dom";
import {
  useGetActivePaymentQuery,
  useGetMerchandiseItemsQuery,
  useGetOpenTabsQuery,
  useGetSalesPosQuery,
} from "../../generated/graphql";
import { encrypt } from "../../utils/Helpers";
import { MeContext } from "../Wrapper";

interface FooterProps {
  onHeightChange: (height: number) => void; // Callback to pass the footer height
}

const Footer: React.FC<FooterProps> = ({ onHeightChange }) => {
  const bgColor = useColorModeValue("blue.100", "blue.800");
  const textColor = useColorModeValue("blue.900", "blue.200");
  // For toasting messages (if you use the Chakra toast hook here)
  const toast = useToast({ position: "top" });

  const me = useContext(MeContext);

  const footerRef = React.useRef<HTMLDivElement>(null);

  const history = useHistory();

  // State for footer statistics
  const [salesCount, setSalesCount] = useState(0);
  const [pendingOrders, setPendingOrders] = useState(0);
  const [pendingBills, setPendingBills] = useState(0);
  const [lowStockItems, setLowStockItems] = useState<
    Array<{ name: string; quantity: number }>
  >([]);
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [apiStatus, setApiStatus] = useState<
    "connected" | "disconnected" | "checking"
  >("checking");
  const [electronNetworkStatus, setElectronNetworkStatus] = useState<{
    online: boolean;
    details: { dns: boolean; internet: boolean };
  } | null>(null);

  const [{ data, fetching: subscriptionFetching }, refetchSubscription] =
    useGetActivePaymentQuery({
      requestPolicy: "network-only",
      variables: { companyId: me?.companyId || 0 },
    });

  useEffect(() => {
    if (!subscriptionFetching && !data?.getActivePayment?.id) {
      setTimeout(async () => {
        await refetchSubscription();
        if (
          !data?.getActivePayment?.id ||
          getDaysRemaining(data?.getActivePayment?.endDate) < -1
        ) {
          const encryptedData = encrypt("expired");
          await window.electron.invoke("save-company-sub", encryptedData);
          history.push("/subscription-expired");
        }
      }, 600000);
    } else if (data?.getActivePayment?.id) {
      const encryptedData = encrypt("active");
      window.electron.invoke("save-company-sub", encryptedData);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data, subscriptionFetching]);

  // Queries
  const [{ data: salesData }] = useGetSalesPosQuery({
    requestPolicy: "cache-and-network",
  });
  const [{ data: openTabsData }] = useGetOpenTabsQuery({
    requestPolicy: "cache-and-network",
  });
  const [{ data: merchandiseData }] = useGetMerchandiseItemsQuery({
    requestPolicy: "cache-and-network",
  });

  // IPC invoke for clearing image cache
  const handleClearCache = async () => {
    try {
      const result = await window.electron.invoke("clear-image-cache", {});
      console.log("result from", result);
      if (result.status) {
        toast({
          title: "Image cache cleared",
          status: "success",
          isClosable: true,
        });
      } else {
        toast({
          title: "Failed to clear image cache",
          status: "error",
          isClosable: true,
        });
      }
    } catch (err: any) {
      console.error(err);
      toast({
        title: "Error clearing image cache",
        status: "error",
        isClosable: true,
      });
    }
  };

  useEffect(() => {
    if (salesData?.getSalesPOS) {
      setSalesCount(salesData.getSalesPOS.length);
    }

    if (openTabsData?.getOpenTabs) {
      setPendingOrders(
        openTabsData.getOpenTabs.reduce((count, order) => {
          const pendingTransfers = order.transfers.filter(
            (transfer) => !transfer.dispatched
          ).length;
          return count + pendingTransfers;
        }, 0)
      );

      setPendingBills(
        openTabsData.getOpenTabs.filter((order) => !order.bill?.cleared).length
      );
    }

    if (merchandiseData?.getMerchandiseItems) {
      setLowStockItems(
        merchandiseData.getMerchandiseItems
          .filter((item) => item.stock <= item.reorder)
          .map((item) => ({ name: item.name, quantity: item.stock }))
      );
    }
  }, [salesData, openTabsData, merchandiseData]);

  useEffect(() => {
    if (footerRef.current) {
      onHeightChange(footerRef.current.offsetHeight);
    }
  }, [footerRef, onHeightChange]);

  // Add network status listener
  useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener("online", handleOnline);
    window.addEventListener("offline", handleOffline);

    return () => {
      window.removeEventListener("online", handleOnline);
      window.removeEventListener("offline", handleOffline);
    };
  }, []);

  // Add API health check with periodic rechecking
  useEffect(() => {
    let intervalId: NodeJS.Timeout;
    let isMounted = true;

    const checkApiConnection = async () => {
      // Use Electron's network status if available, otherwise use browser's
      const networkOnline = electronNetworkStatus
        ? electronNetworkStatus.online
        : isOnline;

      if (!networkOnline) {
        setApiStatus("disconnected");
        return;
      }

      setApiStatus("checking");

      try {
        // Use your GraphQL endpoint with a simple query
        const response = await fetch(
          import.meta.env.VITE_API_URL || "http://localhost:4000/graphql",
          {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({
              query: "{ __typename }",
            }),
            // Short timeout to avoid hanging
            signal: AbortSignal.timeout(5000),
          }
        );

        if (isMounted) {
          setApiStatus(response.ok ? "connected" : "disconnected");
        }
      } catch (error) {
        console.error("Error checking API connection:", error);
        if (isMounted) {
          setApiStatus("disconnected");
        }
      }
    };

    // Initial check
    checkApiConnection();

    // Set up periodic checking every 60 seconds (1 minute)
    intervalId = setInterval(checkApiConnection, 60000);

    return () => {
      isMounted = false;
      clearInterval(intervalId);
    };
  }, [isOnline, electronNetworkStatus]);

  // Add this useEffect to listen for network status from Electron and check periodically
  useEffect(() => {
    let intervalId: NodeJS.Timeout;

    // Function to check network status via Electron
    const checkElectronNetworkStatus = async () => {
      if (window.electron) {
        try {
          const status = await window.electron.invoke(
            "check-network-status",
            {}
          );
          setElectronNetworkStatus(status);
          setIsOnline(status.online);
        } catch (err) {
          console.error("Failed to check network status:", err);
        }
      }
    };

    // Check if we're running in Electron
    if (window.electron) {
      // Initial check
      checkElectronNetworkStatus();

      // Set up periodic checking every 60 seconds (1 minute)
      intervalId = setInterval(checkElectronNetworkStatus, 60000);

      // Listen for status changes
      window.electron.on("network-status-change", (status) => {
        setElectronNetworkStatus(status);
        setIsOnline(status.online);
      });

      return () => {
        // Clean up the interval when the component unmounts
        clearInterval(intervalId);
      };
    }
  }, []);

  // Network and API status indicators as separate components
  const InternetStatusIndicator = () => {
    // Use Electron network status if available, otherwise use browser status
    const networkOnline = electronNetworkStatus
      ? electronNetworkStatus.online
      : isOnline;

    return (
      <Tooltip
        label={
          <Box>
            <Text>
              {networkOnline ? "Internet connected" : "No internet connection"}
            </Text>
          </Box>
        }
        placement="top"
        hasArrow
      >
        <Button
          variant="ghost"
          leftIcon={networkOnline ? <FaWifi /> : <PiWifiSlash />}
          fontSize="sm"
          color={networkOnline ? "green.500" : "red.500"}
          _hover={{ bg: useColorModeValue("blue.200", "blue.700") }}
        >
          {networkOnline ? "Internet" : "No Internet"}
        </Button>
      </Tooltip>
    );
  };

  const ApiStatusIndicator = () => {
    let icon, label, color;

    if (apiStatus === "checking") {
      icon = <Spinner size="xs" />;
      label = "Checking server connection...";
      color = "yellow.500";
    } else if (apiStatus === "connected") {
      icon = <FaServer />;
      label = "Connected to server";
      color = "green.500";
    } else {
      icon = <FaExclamationTriangle />;
      label = "Server unreachable";
      color = "orange.500";
    }

    return (
      <Tooltip label={label} placement="top" hasArrow>
        <Button
          variant="ghost"
          leftIcon={icon}
          fontSize="sm"
          color={color}
          _hover={{ bg: useColorModeValue("blue.200", "blue.700") }}
        >
          {apiStatus === "connected"
            ? "API Online"
            : apiStatus === "checking"
            ? "API Checking..."
            : "API Offline"}
        </Button>
      </Tooltip>
    );
  };

  // Helper function to determine subscription badge color
  const getSubscriptionBadgeColor = (daysRemaining: number) => {
    if (daysRemaining <= 3) return "red";
    if (daysRemaining <= 7) return "yellow";
    return "green";
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    try {
      // Handle both string timestamp and numeric timestamp
      const date = new Date(
        isNaN(Number(dateString)) ? dateString : Number(dateString)
      );
      return date.toLocaleDateString();
    } catch (error) {
      console.error("Error formatting date:", error);
      return "Invalid date";
    }
  };

  // Calculate days remaining
  const getDaysRemaining = (endDateStr: string): number => {
    try {
      const endDate = new Date(
        isNaN(Number(endDateStr)) ? endDateStr : Number(endDateStr)
      );
      const today = new Date();
      const diffTime = endDate.getTime() - today.getTime();
      return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    } catch (error) {
      console.error("Error calculating days remaining:", error);
      return 0;
    }
  };

  return (
    <Box
      ref={footerRef}
      bg={bgColor}
      w="100%"
      borderTop="1px solid"
      borderColor={useColorModeValue("blue.200", "blue.700")}
      position="fixed"
      bottom="0"
      left="0"
      zIndex="10000"
      px={4}
    >
      <Flex align="center" justify="space-between" h="100%">
        {/* Statistics with Transparent Buttons */}
        <Flex align="center" gap={4}>
          {/* Network Status Indicators - now separate */}
          <InternetStatusIndicator />
          <ApiStatusIndicator />

          <Button
            as={Link}
            to="sales"
            variant="ghost"
            leftIcon={<ViewIcon />}
            fontSize="sm"
            color={textColor}
            _hover={{ bg: useColorModeValue("blue.200", "blue.700") }}
          >
            Sales Count: <strong>{salesCount}</strong>
          </Button>
          <Button
            as={Link}
            to="pending-orders"
            variant="ghost"
            leftIcon={<CalendarIcon />}
            fontSize="sm"
            color={textColor}
            _hover={{ bg: useColorModeValue("blue.200", "blue.700") }}
          >
            Pending Orders: <strong>{pendingOrders}</strong>
          </Button>
          <Button
            as={Link}
            to="open-tabs"
            variant="ghost"
            leftIcon={<InfoIcon />}
            fontSize="sm"
            color={textColor}
            _hover={{ bg: useColorModeValue("blue.200", "blue.700") }}
          >
            Pending Bills: <strong>{pendingBills}</strong>
          </Button>

          {/* Subscription Status */}
          {data &&
          data.getActivePayment &&
          data.getActivePayment.id &&
          getDaysRemaining(data.getActivePayment.endDate) <= 31 ? (
            <Tooltip
              label={
                <Box p={2}>
                  <Text fontWeight="bold">Subscription Status</Text>
                  <Text>
                    Expires on: {formatDate(data.getActivePayment.endDate)}
                  </Text>
                  <Text>
                    Days remaining:{" "}
                    {getDaysRemaining(data.getActivePayment.endDate)}
                  </Text>
                  <Text>Status: {data.getActivePayment.status}</Text>
                </Box>
              }
              placement="top"
              hasArrow
            >
              <Button
                variant="ghost"
                leftIcon={<TimeIcon />}
                fontSize="sm"
                color={textColor}
                _hover={{ bg: "blue.200" }}
              >
                Subscription:
                <Badge
                  ml={2}
                  colorScheme={getSubscriptionBadgeColor(
                    getDaysRemaining(data.getActivePayment.endDate)
                  )}
                  borderRadius="full"
                  px={2}
                >
                  {getDaysRemaining(data.getActivePayment.endDate)} days
                </Badge>
              </Button>
            </Tooltip>
          ) : subscriptionFetching ? (
            <Button
              variant="ghost"
              leftIcon={<TimeIcon />}
              fontSize="sm"
              color={textColor}
              _hover={{ bg: "blue.200" }}
              isLoading
            >
              Loading...
            </Button>
          ) : (
            <Tooltip
              label="No active subscription found"
              placement="top"
              hasArrow
            >
              <Button
                variant="ghost"
                leftIcon={<TimeIcon />}
                fontSize="sm"
                color={textColor}
                _hover={{ bg: "blue.200" }}
                onClick={() => {
                  refetchSubscription();
                }}
              >
                Subscription:
                <Badge ml={2} colorScheme="red" borderRadius="full" px={2}>
                  Inactive
                </Badge>
              </Button>
            </Tooltip>
          )}
        </Flex>

        {/* Low Stock Warning and Cache Clear */}
        <Flex align="center" gap={2}>
          <Tooltip
            label={
              lowStockItems.length > 0 ? (
                <Box>
                  <Text fontWeight="bold" mb={1}>
                    Low in Stock:
                  </Text>
                  {lowStockItems.map((item, index) => (
                    <Text key={index} fontSize="sm">
                      {item.name}: {item.quantity}
                    </Text>
                  ))}
                </Box>
              ) : (
                "All items in stock"
              )
            }
            aria-label="Low stock items"
            placement="top"
            hasArrow
            bg={useColorModeValue("gray.700", "gray.200")}
            color={useColorModeValue("white", "black")}
          >
            <IconButton
              icon={<WarningTwoIcon />}
              aria-label="Low Stock Items"
              colorScheme={lowStockItems.length > 0 ? "yellow" : "gray"}
              variant="ghost"
              size="sm"
            />
          </Tooltip>
          {/* Drop-up Menu Button for Clearing Cache */}
          <Menu placement="top">
            <MenuButton
              as={Button}
              size="sm"
              variant="ghost"
              colorScheme="teal"
              leftIcon={<HamburgerIcon />}
            ></MenuButton>
            <MenuList>
              <MenuItem onClick={handleClearCache}>Clear Image Cache</MenuItem>
            </MenuList>
          </Menu>
        </Flex>
      </Flex>
    </Box>
  );
};

export default Footer;
