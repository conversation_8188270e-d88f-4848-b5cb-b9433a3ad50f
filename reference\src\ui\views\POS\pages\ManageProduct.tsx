/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useState, useRef, useEffect, ChangeEvent } from "react";
import { useForm } from "react-hook-form";
import {
  Flex,
  Button,
  FormControl,
  FormLabel,
  Input,
  Text,
  useColorModeValue,
  FormErrorMessage,
  useToast,
  Grid,
  GridItem,
  Image,
  Box,
} from "@chakra-ui/react";
import AsyncCreatableSelect from "react-select/async-creatable";
import { useHistory, useLocation } from "react-router-dom";
import {
  useAddItemMutation,
  useEditItemMutation,
  useGetCategoriesQuery,
  useAddCategoryWithTypeNameMutation,
  Item,
} from "../../../generated/graphql";
import supabase from "../../../supabase";
import { v4 } from "uuid";

interface IManageProductProps {
  item?: any;
}

interface State {
  item?: Item; // item is optional
}

const ManageProduct: React.FC<IManageProductProps> = () => {
  const toast = useToast({ position: "top" });
  const history = useHistory();
  const [, addItem] = useAddItemMutation();
  const [, editItem] = useEditItemMutation();
  const [, addCategory] = useAddCategoryWithTypeNameMutation();

  const [error, setError] = useState("");
  const [category, setCategory] = useState<any>(null);
  // State to store the full list of categories.
  const [allCategories, setAllCategories] = useState<
    { label: string; value: string }[]
  >([]);
  const [isCategoryLoading, setIsCategoryLoading] = useState(false);
  // State for storing the product image URL (for preview).
  const [productImage, setProductImage] = useState<string | null>(null);
  // State to store the selected file (to be uploaded on submit)
  const [file, setFile] = useState<File | undefined>(undefined);
  // State to hold the old image file path for deletion.
  const [oldImagePath, setOldImagePath] = useState<string | null>(null);

  // File input ref for triggering file selection.
  const fileInputRef = useRef<HTMLInputElement>(null);

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors, isSubmitting },
  } = useForm();

  // Get the item from location state.
  const location = useLocation();
  const state = location.state as State;

  const [{ data }] = useGetCategoriesQuery({
    variables: { type: "Item_Category" },
    requestPolicy: "network-only",
  });

  // When categories data arrives, update the full list.
  useEffect(() => {
    if (data?.getCategories) {
      const tempCats = data.getCategories.map((cat) => ({
        value: cat.name,
        label: cat.name,
      }));
      setAllCategories(tempCats);
    }
  }, [data]);

  // When editing, prepopulate form values and image.
  useEffect(() => {
    if (state?.item) {
      reset({
        ...state.item,
        reorder: state.item.reorder.toString(),
      });
      setCategory({
        value: state.item.reference,
        label: state.item.reference,
      });
      if (state.item.image) {
        setProductImage(state.item.image);
        // Extract old image file path (assuming URL format contains "/heal/")
        const parts = state.item.image.split("/heal/");
        if (parts.length === 2) {
          setOldImagePath(parts[1]);
        }
      }
    }
  }, [state?.item, reset]);

  // Filter from the full list of categories.
  const loadCategories = async (inputValue: string) => {
    return allCategories.filter(
      (cat) =>
        !inputValue ||
        cat.label.toLowerCase().includes(inputValue.toLowerCase())
    );
  };

  // Handler for creating a new category.
  const handleCreateCategory = async (inputValue: string) => {
    setIsCategoryLoading(true);
    try {
      const categoryAdded = await addCategory({
        args: { name: inputValue, typeName: "Item_Category" },
      });
      if (categoryAdded.data?.addCategoryWithTypeName.error) {
        setError(categoryAdded.data.addCategoryWithTypeName.error.message);
      }
      if (categoryAdded.error) {
        setError(categoryAdded.error.message);
      } else {
        const newCategory = {
          value: categoryAdded.data?.addCategoryWithTypeName.category?.name,
          label: categoryAdded.data?.addCategoryWithTypeName.category?.name,
        };
        setAllCategories((prev) => [...prev, newCategory as any]);
        setCategory(newCategory);
        toast({
          title: "New category created successfully!",
          status: "success",
          isClosable: true,
        });
      }
    } catch (err: any) {
      setError(err.message || "An error occurred while creating the category.");
    } finally {
      setIsCategoryLoading(false);
    }
  };

  // Handler for file input change.
  // Creates a local preview URL and stores the file for later upload.
  const handleFileChange = (event: ChangeEvent<HTMLInputElement>) => {
    setError("");
    const selectedFile = event.target.files?.[0];
    if (selectedFile) {
      setFile(selectedFile);
      const previewUrl = URL.createObjectURL(selectedFile);
      setProductImage(previewUrl);
    }
  };

  // Trigger file input when "Change Photo" is clicked.
  const triggerFileSelect = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const onSubmit = async (values: any) => {
    setError("");

    if (!category) {
      setError("Please select a product category.");
      return;
    }

    // Build payload.
    const payload: any = {
      name: values.name,
      description: values.description,
      unit: values.unit,
      type: "consumable", // auto-set type
      reorder: Number(values.reorder),
      reference: category?.value,
    };

    // If a file was selected, perform the upload now.
    if (file) {
      const { data: uploadedFile, error: uploadError } = await supabase.storage
        .from("heal")
        .upload(`public/${v4()}`, file);
      if (uploadError) {
        setError(uploadError.message);
        return toast({
          title: "Image could not be uploaded!",
          variant: "left-accent",
          status: "error",
          isClosable: true,
        });
      } else if (uploadedFile) {
        const fileUrl = `https://eqzgvivfuzmyfxbupxht.supabase.co/storage/v1/object/public/heal/${uploadedFile.path}`;
        payload.image = fileUrl;
        // If there's an old image and its file path exists, delete it.
        if (oldImagePath) {
          supabase.storage
            .from("heal")
            .remove([oldImagePath])
            .catch((err) =>
              console.error("Failed to delete old image:", err.message)
            );
        }
      }
    } else if (productImage) {
      // If no new file was selected and productImage exists (from editing), include it.
      payload.image = productImage;
    }

    try {
      if (state?.item) {
        const { data } = await editItem({ id: state.item.id, args: payload });
        if (!data?.editItem?.status) {
          setError(
            data?.editItem?.error?.message || "Failed to update product"
          );
          toast({
            title: "Product update failed!",
            status: "error",
            isClosable: true,
          });
          return;
        }
        toast({
          title: "Product updated successfully!",
          status: "success",
          isClosable: true,
        });
        reset();
        history.push("products");
      } else {
        const { data } = await addItem({ args: payload });
        if (!data?.addItem?.status) {
          setError(data?.addItem?.error?.message || "Failed to add product");
          toast({
            title: "Product add failed!",
            status: "error",
            isClosable: true,
          });
          return;
        }
        toast({
          title: "Product added successfully!",
          status: "success",
          isClosable: true,
        });
        reset();
        history.push("products");
      }
    } catch (err: any) {
      setError(err.message || "An error occurred");
    }
  };

  const textColor = useColorModeValue("gray.700", "white");
  const bgForm = useColorModeValue("white", "navy.800");

  return (
    <Flex
      h="100vh"
      justifyContent="center"
      alignItems="center"
      bg={useColorModeValue("gray.50", "gray.900")}
    >
      <Flex
        direction="column"
        w="720px"
        p="40px"
        bg={bgForm}
        borderRadius="15px"
        boxShadow="lg"
      >
        <Text
          fontSize="xl"
          color={textColor}
          fontWeight="bold"
          textAlign="center"
          mb="12px"
        >
          {state?.item ? "Edit" : "Add"} Product
        </Text>

        {error && (
          <Text mb="10px" color="red.500">
            {error}
          </Text>
        )}

        {/* Product Image Section */}
        <Box mb={6} textAlign="center">
          <Text mb={2} fontWeight="semibold" color={textColor}>
            Product Image
          </Text>
          <Box position="relative" display="inline-block">
            <Image
              src={
                productImage || "https://via.placeholder.com/150?text=No+Image"
              }
              alt="Product"
              boxSize="150px"
              objectFit="cover"
              borderRadius="md"
              boxShadow="md"
            />
            <Button
              size="xs"
              position="absolute"
              bottom="5px"
              right="5px"
              colorScheme="teal"
              onClick={triggerFileSelect}
            >
              Change Photo
            </Button>
          </Box>
          <Input
            ref={fileInputRef}
            type="file"
            accept="image/*"
            display="none"
            onChange={handleFileChange}
          />
          <Text mt={2} fontSize="sm" color={textColor}>
            {state?.item
              ? "Click change photo to update product image"
              : "Upload a product image (optional)"}
          </Text>
        </Box>

        <form onSubmit={handleSubmit(onSubmit)}>
          <Grid templateColumns="repeat(6, 1fr)" gap={6}>
            <FormControl isInvalid={!!errors.name} as={GridItem} colSpan={6}>
              <FormLabel>Product Trade Name</FormLabel>
              <Input
                {...register("name", { required: "Trade name is required" })}
                placeholder="E.g., Kilimanjaro"
              />
              <FormErrorMessage>
                {errors.name?.message as string}
              </FormErrorMessage>
            </FormControl>

            <FormControl
              isInvalid={!!errors.description}
              as={GridItem}
              colSpan={6}
            >
              <FormLabel>Product Generic Name</FormLabel>
              <Input
                {...register("description", {
                  required: "Generic name is required",
                })}
                placeholder="E.g., Mineral Water"
              />
              <FormErrorMessage>
                {errors.description?.message as string}
              </FormErrorMessage>
            </FormControl>

            <FormControl isInvalid={!category} as={GridItem} colSpan={6}>
              <FormLabel>Product Category</FormLabel>
              <AsyncCreatableSelect
                loadOptions={loadCategories}
                cacheOptions={false}
                onCreateOption={handleCreateCategory}
                isLoading={isCategoryLoading}
                isDisabled={isCategoryLoading}
                defaultOptions={allCategories}
                value={category}
                onChange={setCategory}
                placeholder="Select or create product category"
              />
              {!category && (
                <Text mt="2" color="red.500" fontSize="sm">
                  Please select a product category.
                </Text>
              )}
            </FormControl>

            <FormControl isInvalid={!!errors.reorder} as={GridItem} colSpan={3}>
              <FormLabel>Reorder Point</FormLabel>
              <Input
                type="number"
                {...register("reorder", {
                  required: "Reorder point is required",
                })}
                placeholder="E.g., 10"
              />
              <FormErrorMessage>
                {errors.reorder?.message as string}
              </FormErrorMessage>
            </FormControl>

            <FormControl isInvalid={!!errors.unit} as={GridItem} colSpan={3}>
              <FormLabel>Unit</FormLabel>
              <Input
                {...register("unit", { required: "Unit is required" })}
                placeholder="E.g., Litre, Kg"
              />
              <FormErrorMessage>
                {errors.unit?.message as string}
              </FormErrorMessage>
            </FormControl>
          </Grid>

          <Button
            mt={6}
            w="100%"
            colorScheme="teal"
            isLoading={isSubmitting}
            type="submit"
          >
            {state?.item ? "Edit" : "Add"} Product
          </Button>
        </form>
      </Flex>
    </Flex>
  );
};

export default ManageProduct;
