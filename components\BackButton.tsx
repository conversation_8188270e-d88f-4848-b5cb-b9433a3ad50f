import React from "react";
import { TouchableOpacity, StyleSheet } from "react-native";
import { Text } from "react-native-paper";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import { useRouter } from "expo-router";

interface BackButtonProps {
  onPress?: () => void;
  label?: string;
  color?: string;
  size?: number;
  style?: any;
}

export function BackButton({
  onPress,
  label = "Back",
  color = "rgb(31, 114, 161)",
  size = 24,
  style,
}: BackButtonProps) {
  const router = useRouter();

  const handlePress = () => {
    if (onPress) {
      onPress();
    } else {
      router.back();
    }
  };

  return (
    <TouchableOpacity
      style={[styles.backButton, style]}
      onPress={handlePress}
    >
      <MaterialCommunityIcons
        name="arrow-left"
        size={size}
        color={color}
      />
      <Text style={[styles.backButtonText, { color }]}>
        {label}
      </Text>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  backButton: {
    flexDirection: "row",
    alignItems: "center",
    padding: 8,
    borderRadius: 8,
  },
  backButtonText: {
    fontSize: 16,
    fontWeight: "500",
    marginLeft: 4,
  },
});
