import React, { useState } from "react";
import { View, StyleSheet, ScrollView } from "react-native";
import {
  Text,
  Card,
  Switch,
  Button,
  Portal,
  Modal,
  ActivityIndicator,
  Avatar,
} from "react-native-paper";
import { LinearGradient } from "expo-linear-gradient";
import {
  useGetUserQuery,
  useAddPermissionMutation,
  useRemovePermissionMutation,
  useMeQuery,
} from "../../../generated/graphql";
import { useLocalSearchParams } from "expo-router";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import { BackButton } from "../../../components/BackButton";

export default function EmployeeProfileScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();

  const { data, loading, refetch } = useGetUserQuery({
    variables: { id: parseInt(id) },
    fetchPolicy: "network-only",
  });
  const { data: meData } = useMeQuery({
    fetchPolicy: "network-only",
  });

  const [modalVisible, setModalVisible] = useState(false);
  const [selectedPermission, setSelectedPermission] = useState<string | null>(
    null
  );
  const [autoOpened, setAutoOpened] = useState(false);
  const [addPermission, { loading: addLoading }] = useAddPermissionMutation();
  const [removePermission, { loading: removeLoading }] =
    useRemovePermissionMutation();

  const employee = data?.getUser;
  const me = meData?.me;

  const hasPermissionsPermission =
    me?.role?.name === "admin" ||
    me?.permissions?.some((perm) => perm.name === "Permissions");

  const permissionList1 = [
    "POS",
    "Sales",
    "Products",
    "Open Tabs",
    "Counters",
    "Permissions",
    "Pending Orders",
  ];

  const permissionList2 = [
    "Users",
    "Import",
    "Transfer",
    "Categories",
    "Designations",
    "Customer Tags",
    "Expenses",
  ];

  const subPermissions = {
    POS: ["Instant Order", "Hold Order", "Employee Order"],
    Sales: ["All"],
    Products: ["Add", "Edit"],
    "Open Tabs": ["All", "Edit", "Delete"],
    Counters: ["Add", "Edit"],
    Permissions: ["All"],
    "Pending Orders": ["All", "Edit", "No Confirm Dialog", "Delete"],
    Users: ["Add", "Edit"],
    Transfer: ["All"],
    Categories: ["Add", "Edit", "Delete"],
    Designations: ["Add", "Edit", "Delete"],
    "Customer Tags": ["Add", "Edit", "Delete"],
    Expenses: ["All", "Approve"],
  };

  const hasPermission = (permissionName: string) => {
    return (
      employee?.permissions?.some((perm) => perm.name === permissionName) ??
      false
    );
  };

  const handlePermissionChange = async (permission: string, value: boolean) => {
    if (!employee?.id || !hasPermissionsPermission) return;

    try {
      if (value) {
        await addPermission({
          variables: { name: permission, userId: employee.id },
        });

        // Check if this permission has subpermissions
        const hasSubPermissions =
          subPermissions[permission as keyof typeof subPermissions];
        if (hasSubPermissions && hasSubPermissions.length > 0) {
          // Automatically open the subpermissions modal
          setTimeout(() => {
            setAutoOpened(true);
            openSubPermissionsModal(permission);
          }, 500); // Small delay to allow the UI to update
        }
      } else {
        await removePermission({
          variables: { name: permission, userId: employee.id },
        });
      }
      refetch();
    } catch (error) {
      console.error("Error updating permission:", error);
    }
  };

  const openSubPermissionsModal = (permission: string) => {
    setSelectedPermission(permission);
    setModalVisible(true);
    // Don't reset autoOpened here as it might be set by the automatic opening
  };

  const getInitials = (firstName: string = "", lastName: string = "") => {
    return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" />
        <Text style={styles.loadingText}>Loading employee details...</Text>
      </View>
    );
  }

  if (!employee) {
    return (
      <View style={styles.errorContainer}>
        <MaterialCommunityIcons name="alert-circle" size={64} color="#dc3545" />
        <Text style={styles.errorText}>Employee not found</Text>
        <BackButton label="Go Back" style={styles.backButton} />
      </View>
    );
  }

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <BackButton style={styles.backButton} />
        <Text variant="headlineMedium" style={styles.title}>
          Employee Profile
        </Text>
      </View>

      <Card style={styles.profileCard} mode="outlined">
        <Card.Content>
          <View style={styles.profileHeader}>
            <View style={styles.avatarContainer}>
              {employee.image ? (
                <Avatar.Image
                  size={80}
                  source={{ uri: employee.image }}
                  style={styles.avatar}
                />
              ) : (
                <Avatar.Text
                  size={80}
                  label={getInitials(employee.firstname, employee.lastname)}
                  style={styles.avatar}
                  labelStyle={styles.avatarLabel}
                />
              )}
            </View>
            <View style={styles.profileInfo}>
              <Text variant="headlineSmall" style={styles.name}>
                {employee.firstname} {employee.lastname}
              </Text>
              <Text variant="bodyLarge" style={styles.email}>
                {employee.email}
              </Text>
              <View style={styles.roleContainer}>
                <MaterialCommunityIcons
                  name="account-tie"
                  size={20}
                  color="#6c757d"
                />
                <Text variant="bodyMedium" style={styles.role}>
                  {employee.role?.name}
                </Text>
              </View>
            </View>
          </View>
        </Card.Content>
      </Card>

      {hasPermissionsPermission && (
        <>
          <Card style={styles.permissionsCard} mode="outlined">
            <Card.Content>
              <Text variant="titleMedium" style={styles.sectionTitle}>
                Core Permissions
              </Text>
              {permissionList1.map((permission) => (
                <View key={permission} style={styles.permissionItem}>
                  <View style={styles.permissionInfo}>
                    <Text variant="bodyLarge">{permission}</Text>
                    {subPermissions[
                      permission as keyof typeof subPermissions
                    ] && (
                      <Text variant="bodySmall" style={styles.subPermText}>
                        Has sub-permissions
                      </Text>
                    )}
                  </View>
                  <View style={styles.permissionControls}>
                    {subPermissions[
                      permission as keyof typeof subPermissions
                    ] && (
                      <Button
                        mode="outlined"
                        onPress={() => openSubPermissionsModal(permission)}
                        style={styles.expandButton}
                        disabled={addLoading || removeLoading}
                      >
                        Details
                      </Button>
                    )}
                    <Switch
                      value={hasPermission(permission)}
                      onValueChange={(value) =>
                        handlePermissionChange(permission, value)
                      }
                      disabled={
                        !hasPermissionsPermission || addLoading || removeLoading
                      }
                    />
                  </View>
                </View>
              ))}
            </Card.Content>
          </Card>

          <Card style={styles.permissionsCard} mode="outlined">
            <Card.Content>
              <Text variant="titleMedium" style={styles.sectionTitle}>
                Additional Permissions
              </Text>
              {permissionList2.map((permission) => (
                <View key={permission} style={styles.permissionItem}>
                  <View style={styles.permissionInfo}>
                    <Text variant="bodyLarge">{permission}</Text>
                    {subPermissions[
                      permission as keyof typeof subPermissions
                    ] && (
                      <Text variant="bodySmall" style={styles.subPermText}>
                        Has sub-permissions
                      </Text>
                    )}
                  </View>
                  <View style={styles.permissionControls}>
                    {subPermissions[
                      permission as keyof typeof subPermissions
                    ] && (
                      <Button
                        mode="outlined"
                        onPress={() => openSubPermissionsModal(permission)}
                        style={styles.expandButton}
                        disabled={addLoading || removeLoading}
                      >
                        Details
                      </Button>
                    )}
                    <Switch
                      value={hasPermission(permission)}
                      onValueChange={(value) =>
                        handlePermissionChange(permission, value)
                      }
                      disabled={
                        !hasPermissionsPermission || addLoading || removeLoading
                      }
                    />
                  </View>
                </View>
              ))}
            </Card.Content>
          </Card>
        </>
      )}

      <Portal>
        <Modal
          visible={modalVisible}
          onDismiss={() => {
            setModalVisible(false);
            setAutoOpened(false);
          }}
          contentContainerStyle={styles.modal}
          style={styles.modalOverlay}
        >
          <LinearGradient
            colors={["#FFFFFF", "#F8FAFC", "#EBF4FF"]}
            style={styles.modalGradient}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
          >
            {/* Modal Header with Futuristic Design */}
            <View style={styles.modalHeader}>
              <LinearGradient
                colors={["#1F72A1", "#3B82F6", "#6366F1"]}
                style={styles.modalHeaderGradient}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 0 }}
              >
                <MaterialCommunityIcons
                  name="shield-account"
                  size={24}
                  color="#FFFFFF"
                  style={styles.modalIcon}
                />
                <Text variant="titleLarge" style={styles.modalTitle}>
                  {selectedPermission} Permissions
                </Text>
              </LinearGradient>
            </View>

            {autoOpened && (
              <View style={styles.autoOpenedContainer}>
                <LinearGradient
                  colors={["#EBF8FF", "#DBEAFE"]}
                  style={styles.autoOpenedGradient}
                  start={{ x: 0, y: 0 }}
                  end={{ x: 1, y: 0 }}
                >
                  <MaterialCommunityIcons
                    name="information"
                    size={20}
                    color="#3B82F6"
                    style={styles.infoIcon}
                  />
                  <Text variant="bodyMedium" style={styles.autoOpenedMessage}>
                    This permission has sub-permissions. Choose which ones to
                    enable for this user.
                  </Text>
                </LinearGradient>
              </View>
            )}

            {/* Modal Content */}
            <ScrollView
              style={styles.modalContent}
              showsVerticalScrollIndicator={false}
              contentContainerStyle={styles.modalScrollContent}
            >
              {selectedPermission &&
                subPermissions[
                  selectedPermission as keyof typeof subPermissions
                ]?.map((subPerm) => (
                  <View key={subPerm} style={styles.permissionItem}>
                    <Text variant="bodyLarge" style={styles.permissionText}>
                      {subPerm}
                    </Text>
                    <Switch
                      value={hasPermission(`${selectedPermission}>${subPerm}`)}
                      onValueChange={(value) =>
                        handlePermissionChange(
                          `${selectedPermission}>${subPerm}`,
                          value
                        )
                      }
                      disabled={
                        !hasPermissionsPermission || addLoading || removeLoading
                      }
                    />
                  </View>
                ))}
              <Button
                mode="contained"
                onPress={() => setModalVisible(false)}
                style={styles.modalButton}
              >
                Close
              </Button>
            </ScrollView>
          </LinearGradient>
        </Modal>
      </Portal>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f5f5f5",
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "#f5f5f5",
  },
  loadingText: {
    marginTop: 16,
    color: "#666",
  },
  errorContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "#f5f5f5",
    padding: 20,
  },
  errorText: {
    fontSize: 18,
    color: "#666",
    marginTop: 16,
    marginBottom: 24,
  },
  header: {
    padding: 16,
    flexDirection: "row",
    alignItems: "center",
  },
  backButton: {
    marginRight: 8,
  },
  title: {
    color: "#1f2937",
    fontWeight: "600",
  },
  profileCard: {
    margin: 16,
    marginTop: 8,
    borderRadius: 12,
    backgroundColor: "#fff",
  },
  permissionsCard: {
    margin: 16,
    marginTop: 8,
    borderRadius: 12,
    backgroundColor: "#fff",
  },
  profileHeader: {
    flexDirection: "row",
    alignItems: "center",
    gap: 20,
  },
  avatarContainer: {
    borderRadius: 40,
  },
  profileInfo: {
    flex: 1,
  },
  name: {
    color: "#1f2937",
    fontWeight: "600",
  },
  email: {
    color: "#6b7280",
    marginTop: 4,
  },
  roleContainer: {
    flexDirection: "row",
    alignItems: "center",
    gap: 6,
    marginTop: 8,
    backgroundColor: "#f3f4f6",
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    alignSelf: "flex-start",
  },
  role: {
    color: "#4b5563",
    fontWeight: "500",
  },
  sectionTitle: {
    marginBottom: 20,
    color: "#1f2937",
    fontWeight: "600",
  },
  permissionItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: 6,
    paddingHorizontal: 4,
    borderBottomWidth: 1,
    borderBottomColor: "#E2E8F0",
    minHeight: 40,
  },
  permissionInfo: {
    flex: 1,
  },
  subPermText: {
    color: "#6b7280",
    marginTop: 2,
  },
  permissionControls: {
    flexDirection: "row",
    alignItems: "center",
    gap: 12,
  },
  expandButton: {
    borderRadius: 8,
  },
  modalOverlay: {
    backgroundColor: "rgba(0, 0, 0, 0.6)",
    justifyContent: "center",
    alignItems: "center",
    flex: 1,
  },
  modal: {
    backgroundColor: "transparent",
    marginHorizontal: 20,
    marginVertical: 20,
    maxWidth: 500,
    width: "90%",
    borderRadius: 20,
    overflow: "hidden",
    shadowColor: "#1F72A1",
    shadowOffset: {
      width: 0,
      height: 10,
    },
    shadowOpacity: 0.3,
    shadowRadius: 20,
    elevation: 15,
  },
  modalGradient: {
    borderRadius: 20,
    minHeight: 450,
    maxHeight: "80%",
    width: "100%",
  },
  modalHeader: {
    marginBottom: 0,
  },
  modalHeaderGradient: {
    flexDirection: "row",
    alignItems: "center",
    padding: 20,
    paddingBottom: 16,
  },
  modalIcon: {
    marginRight: 12,
  },
  modalTitle: {
    color: "#FFFFFF",
    fontWeight: "bold",
    fontSize: 20,
    flex: 1,
  },
  autoOpenedContainer: {
    marginHorizontal: 20,
    marginTop: 16,
    marginBottom: 16,
    borderRadius: 12,
    overflow: "hidden",
    width: "auto",
  },
  autoOpenedGradient: {
    flexDirection: "row",
    alignItems: "flex-start",
    padding: 16,
    minHeight: 60,
  },
  infoIcon: {
    marginRight: 12,
  },
  autoOpenedMessage: {
    color: "#1E40AF",
    fontWeight: "500",
    flex: 1,
    lineHeight: 20,
  },
  modalContent: {
    flex: 1,
    width: "100%",
  },
  modalScrollContent: {
    padding: 20,
    paddingTop: 0,
    flexGrow: 1,
  },
  permissionText: {
    flex: 1,
    fontSize: 16,
    color: "#1F2937",
    fontWeight: "500",
  },
  modalButton: {
    marginTop: 24,
    borderRadius: 12,
    backgroundColor: "#1F72A1",
    shadowColor: "#1F72A1",
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 6,
  },
  avatar: {
    backgroundColor: "#e8f0fe",
  },
  avatarLabel: {
    color: "#007bff",
    fontSize: 24,
    fontWeight: "600",
  },
});
