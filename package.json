{"name": "talisiam<PERSON>", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start --https", "dev": "expo start --https", "reset-project": "node ./scripts/reset-project.js", "android": "expo start --https --android", "ios": "expo start --https --ios", "web": "expo start --https --web", "test": "jest --watchAll", "lint": "expo lint", "codegen": "node scripts/codegen.js", "build:android": "eas build -p android --profile preview"}, "jest": {"preset": "jest-expo"}, "dependencies": {"@apollo/client": "^3.13.8", "@expo/vector-icons": "^14.0.2", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-picker/picker": "2.11.0", "@react-navigation/bottom-tabs": "^7.2.0", "@react-navigation/native": "^7.0.14", "@supabase/supabase-js": "^2.49.4", "expo": "^53.0.12", "expo-barcode-scanner": "^13.0.1", "expo-blur": "~14.1.5", "expo-camera": "^16.1.10", "expo-constants": "~17.1.6", "expo-crypto": "~14.1.5", "expo-file-system": "~18.1.10", "expo-font": "~13.3.1", "expo-haptics": "~14.1.4", "expo-image": "~2.3.0", "expo-image-picker": "~16.1.4", "expo-linear-gradient": "~14.1.5", "expo-linking": "~7.1.5", "expo-local-authentication": "~16.0.4", "expo-router": "~5.1.0", "expo-splash-screen": "~0.30.9", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.5", "expo-system-ui": "~5.0.9", "expo-web-browser": "~14.2.0", "graphql": "^16.10.0", "jwt-decode": "^4.0.0", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.4", "react-native-chart-kit": "^6.12.0", "react-native-dotenv": "^3.4.11", "react-native-drawer-layout": "^4.1.6", "react-native-gesture-handler": "~2.24.0", "react-native-paper": "^5.13.1", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-svg": "15.11.2", "react-native-web": "^0.20.0", "react-native-webview": "13.13.5", "uuid": "^11.1.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@graphql-codegen/cli": "^5.0.5", "@graphql-codegen/typescript": "^4.1.6", "@graphql-codegen/typescript-operations": "^4.6.0", "@graphql-codegen/typescript-react-apollo": "^4.3.2", "@graphql-codegen/typescript-resolvers": "^4.5.0", "@types/jest": "^29.5.12", "@types/react": "~19.0.10", "@types/react-test-renderer": "^18.3.0", "jest": "^29.2.1", "jest-expo": "~53.0.7", "react-test-renderer": "18.3.1", "typescript": "^5.3.3"}, "private": true, "expo": {"doctor": {"reactNativeDirectoryCheck": {"exclude": ["jwt-decode", "react-native-chart-kit"], "listUnknownPackages": false}}}}