/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useEffect, useState } from "react";
import AsyncSelect from "react-select/async";
import { Item } from "../../../generated/graphql";
import { Box, Text } from "@chakra-ui/react";
import { formatToMoney } from "../../../utils/Helpers";

interface UnitOption {
  label: string;
  value: string;
  price?: number;
}

interface UnitInputProps {
  selectedItem?: Item;
  onChange: (option: UnitOption | null) => void;
  price?: boolean;
}

const UnitInput: React.FC<UnitInputProps> = ({
  selectedItem,
  onChange,
  price = false,
}) => {
  const [unitOptions, setUnitOptions] = useState<UnitOption[]>([]);

  useEffect(() => {
    if (selectedItem?.unit) {
      const option: UnitOption = {
        label: selectedItem.unit,
        value: selectedItem.unit,
        price: selectedItem.sellingPrice,
      };
      setUnitOptions([option]);
    } else {
      setUnitOptions([]);
    }
  }, [selectedItem]);

  const loadUnits = async (searchInput: string): Promise<UnitOption[]> => {
    if (!selectedItem?.unit) return [];

    const searchTerm = searchInput.toLowerCase();
    const units: UnitOption[] = [
      {
        label: selectedItem.unit,
        value: selectedItem.unit,
        price: selectedItem.sellingPrice,
      },
    ];

    return units.filter((unit) =>
      unit.label.toLowerCase().includes(searchTerm)
    );
  };

  const CustomOption = ({ innerRef, innerProps, data }: any) => (
    <Box
      ref={innerRef}
      {...innerProps}
      display="flex"
      justifyContent="space-between"
      p={2}
      _hover={{ bg: "gray.100" }}
      cursor="pointer"
    >
      <Text>{data.label}</Text>
      {price && data.price && (
        <Text ml={2} fontWeight="medium">
          {formatToMoney(data.price.toFixed(2))}
        </Text>
      )}
    </Box>
  );

  return (
    <AsyncSelect<UnitOption>
      isClearable
      isSearchable
      defaultOptions={unitOptions}
      loadOptions={loadUnits}
      onChange={onChange}
      placeholder="Select unit"
      closeMenuOnSelect={true}
      escapeClearsValue={true}
      hideSelectedOptions={true}
      components={{ Option: CustomOption }}
      styles={{
        container: (base) => ({
          ...base,
          width: "100%",
        }),
        control: (base) => ({
          ...base,
          borderColor: "inherit",
          boxShadow: "none",
          "&:hover": {
            borderColor: "inherit",
          },
        }),
        menu: (base) => ({
          ...base,
          zIndex: 9999,
        }),
      }}
      theme={(theme) => ({
        ...theme,
        colors: {
          ...theme.colors,
          primary: "#2D3748",
          primary25: "#EDF2F7",
          primary50: "#E2E8F0",
        },
      })}
    />
  );
};

export default UnitInput;
