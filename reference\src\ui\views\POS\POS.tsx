/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable react-hooks/rules-of-hooks */
/* eslint-disable react-refresh/only-export-components */
import React, { useContext, useState, useEffect, useRef } from "react";
import {
  Grid,
  Box,
  Flex,
  Input,
  Select,
  SimpleGrid,
  Badge,
  Text,
  useColorModeValue,
  InputGroup,
  InputLeftElement,
  HStack,
} from "@chakra-ui/react";
import {
  useGetMerchandiseItemsQuery,
  useGetCategoriesQuery,
  useGetEmployeesQuery,
  User,
} from "../../generated/graphql";
import { AppContext } from "../../AppContext";
import Cart from "./components/Cart";
import { ActionType } from "../../interfaces/Types";
import ProductList from "./components/ProductList";
import { MeContext } from "../../components/Wrapper";
import { AsyncCreatableSelect } from "chakra-react-select";
import { FaBarcode } from "react-icons/fa";
import { hasPermission } from "../../interfaces/Helpers";

interface Unit {
  id: number;
  name: string;
  quantity: number;
  price: number;
}

interface Product {
  id: number;
  name: string;
  image: string;
  stock: number;
  price: number;
  unit: string;
  hold: boolean;
  reference: string; // reference value for filtering by category
  sellingPrice?: number; // added for clarity
  barcode?: string;
  units?: Unit[]; // Optional array of units
}

const POS: React.FC = () => {
  const { dispatch, state } = useContext(AppContext);
  const [search, setSearch] = useState("");
  const [selectedEmployee, setSelectedEmployee] = useState(0);
  const [selectedTable, setSelectedTable] = useState("");
  const [showEmployeeSelect, setShowEmployeeSelect] = useState(false);
  const me = useContext(MeContext);
  const [selectedCategory, setSelectedCategory] = useState<string>("");
  // Fetch item categories
  const [{ data: categoryData, fetching: categoryFetching }] =
    useGetCategoriesQuery({
      variables: { type: "Item_Category" },
      requestPolicy: "network-only",
    });
  const categories = categoryData?.getCategories || [];

  const [allCategories, setAllCategories] = useState<
    { label: string; value: string }[]
  >([]);

  // Fetch employees with role "employee"
  const [{ data: employeeData }] = useGetEmployeesQuery();
  const employees =
    employeeData?.getEmployees.filter((emp) => emp.role?.name === "employee") ||
    [];

  // Fetch merchandise items and obtain the refetch function
  const [{ data: merchandiseData }, reGetProducts] =
    useGetMerchandiseItemsQuery({
      requestPolicy: "network-only",
    });
  const products = merchandiseData?.getMerchandiseItems || [];

  // Effect: Call reGetProducts when cart is emptied (skip initial mount)
  const initialCartRef = useRef(true);
  useEffect(() => {
    if (initialCartRef.current) {
      initialCartRef.current = false;
      return;
    }
    if (state.cart.items.length === 0) {
      reGetProducts();
    }
  }, [state.cart.items, reGetProducts]);

  // Handle search input
  const handleSearch = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearch(event.target.value);
  };
  // Fetch customer tags which could be table names or something else (categories with type "Customer_Tag")

  const [{ data: customerTagData }] = useGetCategoriesQuery({
    variables: { type: "Customer_Tag" },
    requestPolicy: "network-only",
  });
  const customerTags = customerTagData?.getCategories || [];

  // When categories data arrives, update the full list.
  useEffect(() => {
    if (customerTagData?.getCategories) {
      const tempCats = customerTagData.getCategories.map((cat) => ({
        value: cat.name,
        label: cat.name,
      }));
      setAllCategories(tempCats);
    }
  }, [customerTagData]);

  // AsyncCreatableSelect options for customer tags
  const loadOptions = (inputValue: string) =>
    new Promise<any[]>((resolve) => {
      const filtered = customerTags
        .filter((tag) =>
          tag.name.toLowerCase().includes(inputValue.toLowerCase())
        )
        .map((tag) => ({ label: tag.name, value: tag.name }));
      resolve(filtered);
    });

  const handleEmployeeClick = () => {
    setShowEmployeeSelect(true);
  };

  // Add item to cart
  const handleAddToCart = (product: Product, unit: string, price: number) => {
    dispatch({
      type: ActionType.ADD_ITEM_TO_CART,
      cartItem: {
        ...product,
        unit: unit ? unit : product.unit,
        price: unit ? price : product.price,
        quantity: 1,
      },
    });
  };

  const handleCreateTag = (tag: string) => {
    const newCategory = {
      value: tag,
      label: tag,
    };
    setSelectedTable(tag);
    setAllCategories((prev) => [...prev, newCategory as any]);
  };

  // Filter products based on search (by name or barcode)
  const filteredProducts = products.filter(
    (product) =>
      product.name.toLowerCase().includes(search.toLowerCase()) ||
      product.barcode?.toLowerCase().includes(search.toLowerCase())
  );

  // Update product stock by subtracting the quantity already in the cart
  const updatedProducts = filteredProducts.map((product) => {
    const cartItem = state.cart.items.find((item) => item.id === product.id);
    const previousItem = cartItem
      ? state.oldCart.items.find((iic) => iic.id === cartItem.id)
      : null;
    const previousQuantity = previousItem ? previousItem.quantity : 0;

    const availableStock =
      product.stock -
      (cartItem
        ? state.cart_inventory
          ? cartItem.quantity - previousQuantity
          : cartItem.quantity
        : 0);
    return {
      ...product,
      stock: availableStock,
      price: product.sellingPrice, // ensure using sellingPrice if applicable
      category: product.reference || "",
      hold: false,
    };
  });

  const cardBg = useColorModeValue("white", "gray.800");
  const textColor = useColorModeValue("teal.600", "teal.300");

  const selectStyles = {
    flex: "1",
    bg: "transparent",
    borderColor: "gray.300",
    borderWidth: "2px",
    borderRadius: "lg",
    color: textColor,
    fontSize: "md",
    fontWeight: "medium",
    _hover: {
      borderColor: "gray.500",
      boxShadow: "sm",
    },
    _focus: {
      borderColor: "blue.400",
      boxShadow: "0 0 0 1px blue.400",
    },
    transition: "all 0.2s ease-in-out",
  };

  return (
    <Grid templateColumns="3fr 1fr" height="100vh" gap={4}>
      {/* Middle Column: Product Search and List */}
      <Box p={4} flex="1" overflow="hidden">
        {hasPermission(me?.permissions, [
          "POS>Hold Order",
          "POS>Employee Order",
        ]) &&
          !state.cart_inventory && (
            <>
              {" "}
              <Flex mb={4} gap={4} width="100%" flexWrap="nowrap">
                {/* Employee Select */}
                {!showEmployeeSelect ? (
                  <Box
                    p={3}
                    bg={useColorModeValue("gray.400", "gray.900")}
                    borderRadius="md"
                    cursor="pointer"
                    onClick={() => {
                      if (
                        hasPermission(me?.permissions, ["POS>Employee Order"])
                      )
                        handleEmployeeClick();
                    }}
                    _hover={{ bg: useColorModeValue("gray.900", "gray.100") }}
                    transition="background 0.3s"
                    flex="1" // This ensures it takes up 50% of the space.
                  >
                    <Text fontSize="lg" fontWeight="bold" color={textColor}>
                      {me?.firstname} {me?.lastname}
                    </Text>
                  </Box>
                ) : (
                  <Select
                    placeholder="Select Employee"
                    value={selectedEmployee}
                    onChange={(e) =>
                      setSelectedEmployee(Number(e.target.value))
                    }
                    {...selectStyles}
                    flex="1" // Ensures equal width with the other component.
                  >
                    {employees.map((emp) => (
                      <option key={emp.id} value={emp.id}>
                        {emp.firstname} {emp.lastname}
                      </option>
                    ))}
                  </Select>
                )}

                {/* Customer Tag Async Select */}
                <Box flex="1">
                  {" "}
                  {/* Wrap AsyncCreatableSelect inside a Box to apply flex */}
                  <AsyncCreatableSelect
                    cacheOptions
                    onCreateOption={handleCreateTag}
                    loadOptions={loadOptions}
                    defaultOptions={allCategories}
                    onChange={(option) =>
                      setSelectedTable(option ? option.value : "")
                    }
                    value={
                      selectedTable
                        ? { label: selectedTable, value: selectedTable }
                        : null
                    }
                    placeholder="Select or create customer tag"
                    chakraStyles={{
                      control: (provided) => ({
                        ...provided,
                        ...selectStyles,
                        width: "100%", // Ensures it takes full available width within its container
                        height: "40px", // Match Chakra Select height
                      }),
                      input: (provided) => ({
                        ...provided,
                        color: textColor,
                      }),
                      placeholder: (provided) => ({
                        ...provided,
                        color: "gray.500",
                      }),
                      singleValue: (provided) => ({
                        ...provided,
                        color: textColor,
                      }),
                      menu: (provided) => ({
                        ...provided,
                        bg: cardBg,
                        borderRadius: "lg",
                        boxShadow: "lg",
                        border: "1px solid",
                        borderColor: "gray.200",
                      }),
                      option: (provided, state) => ({
                        ...provided,
                        bg: state.isFocused ? "gray.100" : cardBg,
                        color: textColor,
                        _hover: {
                          bg: "gray.200",
                        },
                      }),
                    }}
                  />
                </Box>
              </Flex>
            </>
          )}

        <Flex mb={4}>
          {/* Search Input with Barcode Icon */}
          <InputGroup>
            <InputLeftElement
              pointerEvents="none"
              children={<FaBarcode color="gray.500" />}
            />
            <Input
              placeholder="Scan barcode or type product name/barcode..."
              value={search}
              onChange={handleSearch}
            />
          </InputGroup>
        </Flex>

        <Box
          width="100%"
          overflowX="auto"
          pb={2}
          sx={{
            "&::-webkit-scrollbar": {
              height: "8px",
            },
            "&::-webkit-scrollbar-thumb": {
              background: "gray.300",
              borderRadius: "4px",
            },
            "&::-webkit-scrollbar-thumb:hover": {
              background: "gray.500",
            },
          }}
        >
          <HStack
            spacing={2}
            display="inline-flex"
            verticalAlign="top"
            minWidth="max-content" // Ensures the container is at least as wide as its content
          >
            <Badge
              p={2}
              borderRadius="md"
              bg={selectedCategory === "" ? "blue.500" : "gray.200"}
              color={selectedCategory === "" ? "white" : "gray.800"}
              cursor="pointer"
              onClick={() => setSelectedCategory("")}
              _hover={{ bg: "blue.400", color: "white" }}
              minWidth="100px" // Reasonable minimum width
              maxWidth="200px" // Prevents excessive stretching
              textAlign="center"
              whiteSpace="normal" // Allows text to wrap if needed
            >
              All
            </Badge>
            {categoryFetching ? (
              <Text display="inline-block" p={2}>
                Loading...
              </Text>
            ) : (
              categories.map((category) => (
                <Badge
                  key={category.id}
                  p={2}
                  borderRadius="md"
                  bg={
                    selectedCategory === category.name ? "blue.500" : "gray.200"
                  }
                  color={
                    selectedCategory === category.name ? "white" : "gray.800"
                  }
                  cursor="pointer"
                  onClick={() => setSelectedCategory(category.name)}
                  _hover={{ bg: "blue.400", color: "white" }}
                  minWidth="100px" // Reasonable minimum width
                  maxWidth="200px" // Prevents excessive stretching
                  textAlign="center"
                  whiteSpace="normal" // Allows text to wrap if needed
                >
                  {category.name}
                </Badge>
              ))
            )}
          </HStack>
        </Box>

        {/* Product List */}
        <Box
          flex="1" // Take remaining vertical space
          overflowY="auto"
          maxHeight={{ base: "calc(100vh - 150px)", md: "calc(100vh - 300px)" }}
          sx={{
            "&::-webkit-scrollbar": {
              width: "1px",
            },
            "&::-webkit-scrollbar-thumb": {
              background: "gray.300",
              borderRadius: "4px",
            },
            "&::-webkit-scrollbar-thumb:hover": {
              background: "gray.500",
            },
          }}
        >
          <SimpleGrid
            columns={{
              base: 1, // 1 column on very small screens
              sm: 2, // 2 columns on small screens
              md: 3, // 3 columns on medium screens
              lg: 4, // 4 columns on large screens
            }}
            minChildWidth="250px" // Minimum width for each card
            spacing={4}
            mt={2}
          >
            <ProductList
              products={updatedProducts || []}
              search={search}
              category={selectedCategory}
              onAddToCart={handleAddToCart}
            />
          </SimpleGrid>
        </Box>
      </Box>

      {/* Right Column: Cart */}
      {hasPermission(me?.permissions, [
        "POS>Instant Order",
        "POS>Hold Order",
        "POS>Employee Order",
      ]) && (
        <Box
          bg="transparent"
          p={4}
          shadow="md"
          flex="1" // Take remaining vertical space
          overflowY="auto"
          maxHeight={{ base: "calc(100vh - 60px)", md: "calc(100vh - 110px)" }}
          sx={{
            "&::-webkit-scrollbar": {
              width: "1px",
            },
            "&::-webkit-scrollbar-thumb": {
              background: "gray.300",
              borderRadius: "4px",
            },
            "&::-webkit-scrollbar-thumb:hover": {
              background: "gray.500",
            },
          }}
        >
          <Cart
            selectedEmployee={
              employeeData?.getEmployees.find(
                (emp) => emp.id === selectedEmployee
              ) as User
            }
            selectedTable={selectedTable}
          />
        </Box>
      )}
    </Grid>
  );
};

export default POS;
