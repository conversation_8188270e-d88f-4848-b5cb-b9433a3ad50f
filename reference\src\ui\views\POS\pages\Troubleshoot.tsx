/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useState, useEffect } from "react";
import {
  Box,
  Container,
  Heading,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Badge,
  Button,
  HStack,
  VStack,
  Select,
  Input,
  FormControl,
  FormLabel,
  Flex,
  Text,
  useColorModeValue,
  Card,
  CardBody,
  IconButton,
  Tooltip,
  Spinner,
  Alert,
  AlertIcon,
} from "@chakra-ui/react";
import {
  ChevronLeftIcon,
  ChevronRightIcon,
  RepeatIcon,
} from "@chakra-ui/icons";
import {
  useGetCompanyErrorsQuery,
  useGetCompanyQuery,
  useTriggerSyncMutation,
} from "../../../generated/graphql";

const ITEMS_PER_PAGE = 10;

const Troubleshoot: React.FC = () => {
  const getCompanyIdFromStorage = async () => {
    try {
      const oldRes = await window.electron.invoke("get-company-id", {});
      if (oldRes && oldRes.companyId) {
        setSelectedCompany(Number(oldRes.companyId));
      } else {
        console.error("No company ID found in storage");
      }
    } catch (error) {
      console.error("Error fetching company ID:", error);
    }
  };

  useEffect(() => {
    getCompanyIdFromStorage();
  }, []);

  const [selectedCompany, setSelectedCompany] = useState<number | null>(null);
  const [startDate, setStartDate] = useState<string>("");
  const [endDate, setEndDate] = useState<string>("");
  const [severity, setSeverity] = useState<string>("");
  const [currentPage, setCurrentPage] = useState(1);
  const [sortField, setSortField] = useState<"timestamp" | "severity">(
    "timestamp"
  );
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc");
  const [{ fetching: syncFetching }, triggerSync] = useTriggerSyncMutation();

  const bgColor = useColorModeValue("white", "gray.800");
  const borderColor = useColorModeValue("gray.200", "gray.700");
  const textColor = useColorModeValue("gray.700", "white");

  const [{ data: companyData }] = useGetCompanyQuery({
    variables: { id: selectedCompany || 0 },
    pause: !selectedCompany,
  });

  const [{ data, fetching, error }] = useGetCompanyErrorsQuery({
    variables: {
      companyId: selectedCompany || 0,
      startDate: startDate || undefined,
      endDate: endDate || undefined,
      severity: severity || undefined,
    },
    pause: !selectedCompany,
  });

  const sortedErrors = React.useMemo(() => {
    if (!data?.getCompanyErrors) return [];

    return [...data.getCompanyErrors].sort((a, b) => {
      if (sortField === "timestamp") {
        return sortOrder === "asc"
          ? new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
          : new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime();
      } else {
        return sortOrder === "asc"
          ? (a.severity || "").localeCompare(b.severity || "")
          : (b.severity || "").localeCompare(a.severity || "");
      }
    });
  }, [data?.getCompanyErrors, sortField, sortOrder]);

  const paginatedErrors = React.useMemo(() => {
    const startIndex = (currentPage - 1) * ITEMS_PER_PAGE;
    return sortedErrors.slice(startIndex, startIndex + ITEMS_PER_PAGE);
  }, [sortedErrors, currentPage]);

  const totalPages = Math.ceil((sortedErrors?.length || 0) / ITEMS_PER_PAGE);

  const handleResetFilters = () => {
    setStartDate("");
    setEndDate("");
    setSeverity("");
    setCurrentPage(1);
  };

  const handleSort = (field: "timestamp" | "severity") => {
    if (sortField === field) {
      setSortOrder(sortOrder === "asc" ? "desc" : "asc");
    } else {
      setSortField(field);
      setSortOrder("desc");
    }
  };

  const handleSync = async () => {
    try {
      const result = await triggerSync({
        companyId: selectedCompany || 0,
      });
      if (result.data?.triggerSync.status) {
        return alert("Sync completed successfully");
      }
      return alert("Sync failed: Sync not successful due to server error!");
    } catch (error: any) {
      return alert("Sync failed: " + error.message);
    }
  };

  if (!selectedCompany) {
    return (
      <Container maxW="container.xl" py={8}>
        <Alert status="warning">
          <AlertIcon />
          No company ID found. Please select a company first.
        </Alert>
      </Container>
    );
  }

  if (fetching) {
    return (
      <Container maxW="container.xl" py={8}>
        <Flex justify="center" align="center" minH="50vh">
          <Spinner size="xl" />
        </Flex>
      </Container>
    );
  }

  if (error) {
    return (
      <Container maxW="container.xl" py={8}>
        <Alert status="error">
          <AlertIcon />
          Error loading error logs: {error.message}
        </Alert>
      </Container>
    );
  }

  if (!data?.getCompanyErrors || data.getCompanyErrors.length === 0) {
    return (
      <Container maxW="container.xl" py={8}>
        <Alert status="info">
          <AlertIcon />
          No error logs found for this company.
        </Alert>
      </Container>
    );
  }

  return (
    <Flex
      direction="column"
      pt={{ base: "20px", md: "25px" }}
      pb="0px"
      flex="1"
      overflowY="auto"
      maxHeight={{ base: "calc(100vh - 0px)", md: "calc(100vh - 0px)" }}
      sx={{
        "&::-webkit-scrollbar": {
          width: "1px",
        },
        "&::-webkit-scrollbar-thumb": {
          background: "gray.300",
          borderRadius: "4px",
        },
        "&::-webkit-scrollbar-thumb:hover": {
          background: "gray.500",
        },
      }}
    >
      <Container maxW="container.xl">
        <VStack spacing={8} align="stretch">
          <Flex
            justify="space-between"
            align="center"
            mb={10}
            direction={{ base: "column", md: "row" }}
            gap={4}
          >
            <Text
              fontSize="3xl"
              fontWeight="extrabold"
              textAlign="center"
              bgGradient="linear(to-r, teal.400, blue.500)"
              bgClip="text"
              textShadow="1px 1px 3px rgba(0, 0, 0, 0.2)"
              p={2}
            >
              Error Logs - {companyData?.getCompany?.name || "Loading..."}
            </Text>
            <Button
              colorScheme="blue"
              isLoading={syncFetching}
              loadingText="Syncing..."
              onClick={handleSync}
              leftIcon={<RepeatIcon />}
              size="md"
            >
              Trigger Sync
            </Button>
          </Flex>

          <Card bg={bgColor} border="1px" borderColor={borderColor}>
            <CardBody>
              <VStack spacing={4} align="stretch">
                <Heading size="md" color={textColor}>
                  Filter Errors
                </Heading>
                <HStack spacing={4} wrap="wrap">
                  <FormControl minW="200px">
                    <FormLabel>Start Date</FormLabel>
                    <Input
                      type="date"
                      value={startDate}
                      onChange={(e) => setStartDate(e.target.value)}
                    />
                  </FormControl>

                  <FormControl minW="200px">
                    <FormLabel>End Date</FormLabel>
                    <Input
                      type="date"
                      value={endDate}
                      onChange={(e) => setEndDate(e.target.value)}
                    />
                  </FormControl>

                  <FormControl minW="200px">
                    <FormLabel>Severity</FormLabel>
                    <Select
                      value={severity}
                      onChange={(e) => setSeverity(e.target.value)}
                    >
                      <option value="">All</option>
                      <option value="critical">Critical</option>
                      <option value="high">High</option>
                      <option value="medium">Medium</option>
                      <option value="low">Low</option>
                    </Select>
                  </FormControl>

                  <Button
                    colorScheme="blue"
                    variant="outline"
                    onClick={handleResetFilters}
                    alignSelf="flex-end"
                  >
                    Reset Filters
                  </Button>
                </HStack>
              </VStack>
            </CardBody>
          </Card>

          <Card bg={bgColor} border="1px" borderColor={borderColor}>
            <CardBody>
              <Box overflowX="auto">
                <Table variant="simple">
                  <Thead>
                    <Tr>
                      <Th
                        cursor="pointer"
                        onClick={() => handleSort("timestamp")}
                      >
                        Timestamp
                        {sortField === "timestamp" && (
                          <Text as="span" ml={2}>
                            {sortOrder === "asc" ? "↑" : "↓"}
                          </Text>
                        )}
                      </Th>
                      <Th>Level</Th>
                      <Th>Message</Th>
                      <Th
                        cursor="pointer"
                        onClick={() => handleSort("severity")}
                      >
                        Severity
                        {sortField === "severity" && (
                          <Text as="span" ml={2}>
                            {sortOrder === "asc" ? "↑" : "↓"}
                          </Text>
                        )}
                      </Th>
                      <Th>Action</Th>
                    </Tr>
                  </Thead>
                  <Tbody>
                    {paginatedErrors.map((error, index) => (
                      <Tr key={index}>
                        <Td>{new Date(error.timestamp).toLocaleString()}</Td>
                        <Td>
                          <Badge
                            colorScheme={
                              error.level === "ERROR" ? "red" : "yellow"
                            }
                          >
                            {error.level}
                          </Badge>
                        </Td>
                        <Td maxW="300px">
                          <Tooltip label={error.message}>
                            <Text isTruncated>{error.message}</Text>
                          </Tooltip>
                        </Td>
                        <Td>
                          <Badge
                            colorScheme={
                              error.severity === "critical"
                                ? "red"
                                : error.severity === "high"
                                ? "orange"
                                : error.severity === "medium"
                                ? "yellow"
                                : "green"
                            }
                          >
                            {error.severity}
                          </Badge>
                        </Td>
                        <Td>{error.action}</Td>
                      </Tr>
                    ))}
                  </Tbody>
                </Table>
              </Box>

              <Flex justify="space-between" align="center" mt={4}>
                <Text>
                  Showing {paginatedErrors.length} of {sortedErrors.length}{" "}
                  errors
                </Text>
                <HStack>
                  <IconButton
                    aria-label="Previous page"
                    icon={<ChevronLeftIcon />}
                    isDisabled={currentPage === 1}
                    onClick={() => setCurrentPage(currentPage - 1)}
                  />
                  <Text>
                    Page {currentPage} of {totalPages}
                  </Text>
                  <IconButton
                    aria-label="Next page"
                    icon={<ChevronRightIcon />}
                    isDisabled={currentPage === totalPages}
                    onClick={() => setCurrentPage(currentPage + 1)}
                  />
                </HStack>
              </Flex>
            </CardBody>
          </Card>
        </VStack>
      </Container>
    </Flex>
  );
};

export default Troubleshoot;
