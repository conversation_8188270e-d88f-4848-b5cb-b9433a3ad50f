import { useEffect } from "react";
import { Route, Redirect } from "react-router-dom";
import { useMeQuery } from "../generated/graphql";

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const UnAuthRoute = ({ component: Component, location, ...rest }: any) => {
  const [{ data, fetching }] = useMeQuery({ requestPolicy: "network-only" });
  useEffect(() => {
    console.log("Checking if not authenticated.", fetching, data?.me);
  }, [data, fetching]);

  return (
    <Route
      {...rest}
      render={(props: JSX.IntrinsicAttributes) =>
        data?.me?.id ? (
          <Redirect
            to={
              location.search.includes("next=")
                ? location.search.split("next=")[1]
                : `/desktop/POS`
            }
          />
        ) : (
          <Component {...props} />
        )
      }
    />
  );
};

export default UnAuthRoute;
