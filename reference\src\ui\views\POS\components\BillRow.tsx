/* eslint-disable @typescript-eslint/no-unused-vars */
import { useContext, useState } from "react";
import {
  Item,
  Transfer,
  useEditBillItemMutation,
} from "../../../generated/graphql";
import {
  Button,
  Flex,
  Input,
  Td,
  Text,
  Tr,
  IconButton,
  useToast,
  Spinner,
  Box,
} from "@chakra-ui/react";
import { formatToMoney } from "../../../utils/Helpers";
import { AddIcon, MinusIcon } from "@chakra-ui/icons"; // Icons for increase/decrease
import { hasPermission } from "../../../interfaces/Helpers";
import { MeContext } from "../../../components/Wrapper";

const TransferRow: React.FC<{
  transfer: Transfer;
  item: Item;
  deleteTransfer: (transferId: number, inventoryId: number) => void; // Delete function passed as a prop
  deleteStatus: "idle" | "loading" | "success" | "error"; // To manage delete loading state
  setDeleteStatus: (status: "idle" | "loading" | "success" | "error") => void; // To set delete loading state
}> = ({
  transfer: receivedTransfer,
  item,
  deleteTransfer,
  deleteStatus,
  setDeleteStatus,
}) => {
  const toast = useToast({
    position: "top",
  });
  // Local state to manage editing
  const [transfer, setTransfer] = useState<Transfer>(receivedTransfer);
  const [isEditing, setIsEditing] = useState(false);
  const [quantity, setQuantity] = useState<number>(transfer.quantity);
  const [, editBillItem] = useEditBillItemMutation();
  const me = useContext(MeContext);

  const [originalQuantity, setOriginalQuantity] = useState<number>(
    transfer.quantity
  ); // To store the original quantity
  const [status, setStatus] = useState<
    "idle" | "loading" | "success" | "error"
  >("idle");

  // Dummy update function - replace with your actual updateBillItem that accepts quantity.
  const handleSave = async () => {
    setStatus("loading");
    try {
      // Update the bill item quantity.
      const newTansfer = await editBillItem({
        inventoryId: transfer.inventoryId,
        transferId: transfer.id,
        newQuantity: quantity,
      });
      // check errors first and if the status is true set transer
      if (newTansfer.error) throw new Error(newTansfer.error.message);
      else if (!newTansfer.data?.editBillItem.status)
        throw new Error(newTansfer.data?.editBillItem.error?.message);
      else if (newTansfer.data?.editBillItem.status) {
        // Successful update
        toast({
          title: "Quantity updated successfully!",
          status: "success",
          isClosable: true,
        });
        setTransfer(newTansfer.data.editBillItem.transfer as Transfer);
      }
      setStatus("success");
      setTimeout(() => {
        setIsEditing(false);
        setStatus("idle");
      }, 1000);
    } catch (error) {
      setStatus("error");
      setTimeout(() => {
        setStatus("idle");
      }, 1000);
    }
  };

  // Increase or Decrease quantity
  const handleIncrement = () => {
    setQuantity((prev) => prev + 1);
  };

  const handleDecrement = () => {
    setQuantity((prev) => (prev > 0 ? prev - 1 : 0));
  };

  // Cancel editing - reset to original quantity and exit edit mode
  const handleCancel = () => {
    setQuantity(originalQuantity); // Reset quantity to original
    setIsEditing(false); // Exit editing mode
  };

  // Handle the delete action and set the delete status
  const handleDelete = async () => {
    setDeleteStatus("loading");
    try {
      // Call the delete function passed from the parent
      deleteTransfer(transfer.id, transfer.inventoryId);
      setDeleteStatus("success");
      toast({
        title: "Item removed from bill!",
        status: "success",
        isClosable: true,
      });
    } catch (error) {
      setDeleteStatus("error");
      toast({
        title: "Error removing item from bill!",
        status: "error",
        isClosable: true,
      });
    }
  };

  return (
    <Tr>
      <Td>{item?.name}</Td>
      <Td>
        {isEditing ? (
          <Flex align="center">
            {/* Cancel Button */}
            <Button size="xs" colorScheme="gray" onClick={handleCancel} mr={2}>
              Cancel
            </Button>
            {/* Decrease Button */}
            <IconButton
              icon={<MinusIcon />}
              size="xs"
              onClick={handleDecrement}
              aria-label="Decrease quantity"
              mr={2}
            />
            <Input
              value={quantity}
              onChange={(e) => setQuantity(Number(e.target.value))}
              size="xs"
              width="60px"
              mr={2}
              textAlign="center"
            />
            {/* Increase Button */}
            <IconButton
              icon={<AddIcon />}
              size="xs"
              onClick={handleIncrement}
              aria-label="Increase quantity"
              ml={2}
            />
            {status === "loading" ? (
              <Text fontSize="sm" color="gray.500">
                Saving...
              </Text>
            ) : status === "success" ? (
              <Text fontSize="sm" color="green.500">
                &#10003;
              </Text>
            ) : status === "error" ? (
              <Text fontSize="sm" color="red.500">
                &#10007;
              </Text>
            ) : (
              <Button size="xs" colorScheme="green" onClick={handleSave}>
                Save
              </Button>
            )}
          </Flex>
        ) : (
          `${transfer.quantity} ${item?.unit}`
        )}
      </Td>
      <Td>{formatToMoney(item.sellingPrice)}</Td>
      <Td>{formatToMoney(transfer.price)}</Td>
      {hasPermission(me?.permissions, [
        "Open Tabs>Edit",
        "Open Tabs>Delete",
      ]) && (
        <Td>
          <Button
            size="xs"
            colorScheme="blue"
            mr={2}
            onClick={() => setIsEditing(true)}
          >
            Edit
          </Button>
          {/* Delete Button */}
          {hasPermission(me?.permissions, ["Open Tabs>Delete"]) && (
            <Button
              size="xs"
              colorScheme="red"
              onClick={handleDelete}
              isLoading={deleteStatus === "loading"}
              loadingText="Deleting"
              spinner={<Spinner size="xs" />}
            >
              {deleteStatus === "loading" ? (
                <Box as="span" display="inline-block" ml={2}>
                  Deleting...
                </Box>
              ) : (
                "Delete"
              )}
            </Button>
          )}
        </Td>
      )}
    </Tr>
  );
};

export default TransferRow;
