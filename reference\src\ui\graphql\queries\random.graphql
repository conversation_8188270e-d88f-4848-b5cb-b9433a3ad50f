# permission queries
query getPermissions {
  getPermissions {
    id
    name
    roles {
      id
      name
    }
  }
}

# features queries
query getFeatures {
  getFeatures {
    id
    name
    companies {
      id
      name
      type
    }
  }
}

# types queries
query getTypes {
  getTypes {
    id
    name
    description
    category {
      id
      name
    }
  }
}

query getType($id: Float!) {
  getType(id: $id) {
    id
    name
    description
    category {
      id
      name
    }
  }
}

# roles queries
query getRoles {
  getRoles {
    id
    sys
    name
  }
}

query getRole($name: String!) {
  getRole(name: $name) {
    id
    name
    permissions {
      id
      name
    }
  }
}

# categories queries
query getAllCategories {
  getAllCategories {
    id
    name
  }
}

query getCategories($type: String!) {
  getCategories(type: $type) {
    id
    name
  }
}

# expense queries
query expenses($filter: ExpenseFilterInput) {
  expenses(filter: $filter) {
    id
    expenseDate
    authorizer {
      id
    }
    requester {
      id
    }
    assetType
    type
    status
    title
    details
    amount
    createdAt
    updatedAt
  }
}

query getExpenses {
  getExpenses {
    id
    expenseDate
    authorizer {
      id
    }
    requester {
      id
    }
    assetType
    type
    status
    title
    details
    amount
    createdAt
    updatedAt
  }
}

query getExpense($id: Float!) {
  getExpense(id: $id) {
    id
    expenseDate
    authorizer {
      id
    }
    requester {
      id
    }
    assetType
    type
    status
    title
    details
    amount
    createdAt
    updatedAt
  }
}

# get active payment
query getActivePayment($companyId: Float!) {
  getActivePayment(companyId: $companyId) {
    id
    status
    startDate
    endDate
    packageName
  }
}
