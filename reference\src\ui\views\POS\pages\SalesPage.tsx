import React, { useState, useEffect, useContext } from "react";
import {
  <PERSON>lex,
  Button,
  Text,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Badge,
  useDisclosure,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  Box,
  Input,
} from "@chakra-ui/react";
import { Inventory, useGetSalesPosQuery } from "../../../generated/graphql";
import { formatToMoney } from "../../../utils/Helpers";
import { MeContext } from "../../../components/Wrapper";
import { hasPermission } from "../../../interfaces/Helpers";

const SalesPage: React.FC = () => {
  const [{ data: salesData }, refetchSales] = useGetSalesPosQuery({
    requestPolicy: "network-only",
  });

  const { isOpen, onOpen, onClose } = useDisclosure();
  const [selectedSale, setSelectedSale] = useState<Inventory | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const me = useContext(MeContext);

  useEffect(() => {
    const interval = setInterval(() => {
      refetchSales({ requestPolicy: "network-only" });
    }, 5000);

    return () => clearInterval(interval);
  }, [refetchSales]);

  const filteredSales = salesData?.getSalesPOS
    .filter((sale) => {
      if (hasPermission(me?.permissions, ["Sales>All"])) return true;
      else return sale.consumer?.id === me?.id || sale.keeper?.id === me?.id;
    })
    .filter((sale) => {
      const matchesCustomerTag = sale.customerTag
        ?.toLowerCase()
        .includes(searchTerm.toLowerCase());
      const matchesAttending = sale.consumer
        ? `${sale.consumer.user.firstname} ${sale.consumer.user.lastname}`
            .toLowerCase()
            .includes(searchTerm.toLowerCase())
        : sale.keeper
        ? `${sale.keeper.user.firstname} ${sale.keeper.user.lastname}`
            .toLowerCase()
            .includes(searchTerm.toLowerCase())
        : false;

      const matchesItem = sale.items.some((item) =>
        item.name.toLowerCase().includes(searchTerm.toLowerCase())
      );

      return matchesCustomerTag || matchesAttending || matchesItem;
    });

  const handleViewDetails = (sale: Inventory) => {
    setSelectedSale(sale);
    onOpen();
  };

  return (
    <Flex
      direction="column"
      position="relative"
      mb="40px"
      mt="50px"
      flex="1" // Take remaining vertical space
      overflowY="auto"
      maxHeight={{ base: "calc(100vh - 0px)", md: "calc(100vh - 0px)" }}
      sx={{
        "&::-webkit-scrollbar": {
          width: "1px",
        },
        "&::-webkit-scrollbar-thumb": {
          background: "gray.300",
          borderRadius: "4px",
        },
        "&::-webkit-scrollbar-thumb:hover": {
          background: "gray.500",
        },
      }}
    >
      <Flex
        w="100%"
        mx="auto"
        justifyContent="space-between"
        mb="20px"
        pt={{ md: "0px" }}
        direction="column"
      >
        <Flex direction="column" overflow="auto">
          <Text
            fontSize="3xl"
            fontWeight="extrabold"
            textAlign="center"
            mb="12px"
            bgGradient="linear(to-r, teal.400, blue.500)"
            bgClip="text"
            textShadow="1px 1px 3px rgba(0, 0, 0, 0.2)"
            p={2}
          >
            Sales
          </Text>

          <Input
            placeholder="Search by item name, customer tag, or attending employee"
            size="md"
            mb="16px"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />

          <Table variant="simple">
            <Thead>
              <Tr>
                <Th>Bill Amount</Th>
                <Th>Attending</Th>
                <Th>Customer Tag</Th>
                <Th>Status</Th>
                <Th>Details</Th>
              </Tr>
            </Thead>
            <Tbody>
              {filteredSales?.map((sale) => (
                <Tr key={sale.id}>
                  <Td>{formatToMoney(Number(sale.bill?.amount))}</Td>
                  <Td>
                    {sale.consumer && sale.consumer.id > 0
                      ? `${sale.consumer.user.firstname} ${sale.consumer.user.lastname}`
                      : sale.keeper && sale.keeper.id > 0
                      ? `${sale.keeper?.user.firstname} ${sale.keeper?.user.lastname}`
                      : ""}
                  </Td>
                  <Td>{sale.customerTag}</Td>
                  <Td>
                    {sale.bill?.cleared ? (
                      <Badge
                        colorScheme="green"
                        variant="solid"
                        borderRadius="full"
                        px={3}
                        py={1}
                      >
                        Paid
                      </Badge>
                    ) : (
                      <Badge
                        colorScheme="red"
                        variant="solid"
                        borderRadius="full"
                        px={3}
                        py={1}
                      >
                        Unpaid
                      </Badge>
                    )}
                  </Td>
                  <Td>
                    <Button
                      size="sm"
                      colorScheme="teal"
                      onClick={() => handleViewDetails(sale as Inventory)}
                      _hover={{
                        bg: "teal.600",
                        transform: "scale(1.05)",
                      }}
                      transition="all 0.3s"
                    >
                      View
                    </Button>
                  </Td>
                </Tr>
              ))}
            </Tbody>
          </Table>
        </Flex>
      </Flex>

      <Modal isOpen={isOpen} onClose={onClose} size="xl">
        <ModalOverlay />
        <ModalContent bg="gray.50" borderRadius="10px" boxShadow="lg">
          <ModalHeader
            fontSize="3xl"
            color="teal.600"
            textAlign="center"
            fontWeight="bold"
          >
            Sale Details
          </ModalHeader>
          <ModalBody>
            {selectedSale && (
              <Box
                bg="white"
                p={6}
                borderRadius="8px"
                boxShadow="md"
                borderWidth="1px"
                borderColor="gray.200"
                mb={6}
              >
                <Table variant="simple" size="md">
                  <Thead>
                    <Tr>
                      <Th fontSize="md" color="gray.600" fontWeight="bold">
                        Item Name
                      </Th>
                      <Th fontSize="md" color="gray.600" fontWeight="bold">
                        Quantity
                      </Th>
                      <Th fontSize="md" color="gray.600" fontWeight="bold">
                        Sale Amount
                      </Th>
                    </Tr>
                  </Thead>
                  <Tbody>
                    {selectedSale.transfers.map((transfer) => {
                      const item = selectedSale.items.find(
                        (mappedItem) => mappedItem.id === transfer.itemId
                      );
                      return (
                        <Tr key={transfer.id}>
                          <Td>{item?.name}</Td>
                          <Td>
                            {transfer.quantity} {item?.unit}
                          </Td>
                          <Td>
                            {formatToMoney(transfer.price * transfer.quantity)}
                          </Td>
                        </Tr>
                      );
                    })}
                  </Tbody>
                </Table>
              </Box>
            )}
          </ModalBody>
          <ModalFooter>
            <Button
              colorScheme="blue"
              mr={3}
              onClick={onClose}
              _hover={{ bg: "blue.600" }}
              fontWeight="bold"
              fontSize="lg"
              px={8}
            >
              Close
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </Flex>
  );
};

export default SalesPage;
