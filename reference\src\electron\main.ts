/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
import { app, BrowserWindow, ipcMain, session, protocol, net } from "electron";
import path from "path";
import fs from "fs";
import https from "https";
import { fileURLToPath } from "url";
import { getPreloadPath } from "./pathResolver.js";
import { isDev } from "./utils.js";
import contextMenu from "electron-context-menu";
import crypto, { BinaryLike } from "crypto";
import axios from "axios";
import { execSync } from "child_process";
import electronUpdater from "electron-updater";
import log from "electron-log";
import { networkInterfaces } from "os";
import * as netsh from "net";
import { spawn, exec } from "child_process";

const { autoUpdater } = electronUpdater;

// Logging for auto-updater
autoUpdater.logger = log;
log.transports.file.level = "info";
log.info("App starting...");

autoUpdater.autoDownload = true;
autoUpdater.allowDowngrade = false;
autoUpdater.autoInstallOnAppQuit = false;

let mainWindow: BrowserWindow | null = null;
let apiProcess: any = null;

const gotTheLock = app.requestSingleInstanceLock();
if (!gotTheLock) {
  console.log("Another instance is already running - quitting");
  app.exit(0);
}

app.on("second-instance", () => {
  if (mainWindow) {
    if (mainWindow.isMinimized()) mainWindow.restore();
    mainWindow.focus();
  }
});

autoUpdater.on("checking-for-update", () =>
  log.info("Checking for updates...")
);
autoUpdater.on("update-available", (info) =>
  mainWindow?.webContents.send("update-available", info)
);
autoUpdater.on("update-not-available", () =>
  mainWindow?.webContents.send("update-not-available")
);
autoUpdater.on("error", (err) => {
  log.error("Auto-updater error:", err);
  mainWindow?.webContents.send("update-error", err.message);
});
autoUpdater.on("download-progress", (progressObj) =>
  mainWindow?.webContents.send("download-progress", progressObj)
);
autoUpdater.on("update-downloaded", (info) =>
  mainWindow?.webContents.send("update-downloaded", info)
);

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

let httpsOptions: https.ServerOptions;
if (isDev()) {
  httpsOptions = {
    key: fs.readFileSync(
      path.join(__dirname, "../certificates/localhost-key.pem")
    ),
    cert: fs.readFileSync(
      path.join(__dirname, "../certificates/localhost.pem")
    ),
  };
}

const cacheDir = path.join(app.getPath("userData"), "imageCache");
if (!fs.existsSync(cacheDir)) {
  fs.mkdirSync(cacheDir);
}

function getCacheFilePath(url: crypto.BinaryLike | URL) {
  const hash = crypto
    .createHash("md5")
    .update(url as BinaryLike)
    .digest("hex");
  const ext = path.extname(new URL(url as URL).pathname) || ".jpg";
  return path.join(cacheDir, `${hash}${ext}`);
}

async function cacheImage(url: string) {
  try {
    const localPath = getCacheFilePath(url);
    if (fs.existsSync(localPath)) return;
    const response = await axios.get(url, { responseType: "arraybuffer" });
    fs.writeFileSync(localPath, response.data);
  } catch (err: any) {
    console.error(`Failed to cache image from ${url}:`, err.message);
  }
}

function checkNetworkStatus() {
  return {
    online: net.isOnline(),
    details: {
      dns: net.isOnline(),
      internet: net.isOnline(),
    },
  };
}

let networkCheckInterval: NodeJS.Timeout;

function setupNetworkMonitoring(win: BrowserWindow) {
  // Initial check
  const initialStatus = checkNetworkStatus();
  win.webContents.send("network-status-change", initialStatus);

  // Check API connectivity
  const checkApiConnectivity = async () => {
    try {
      const apiUrl =
        process.env.VITE_API_URL || "http://localhost:4000/graphql";
      const response = await axios({
        url: apiUrl,
        method: "POST",
        data: { query: "{ __typename }" },
        timeout: 5000,
      });

      return response.status === 200;
    } catch (error) {
      return false;
    }
  };

  // Combined check function
  const checkConnectivity = async () => {
    const networkStatus = checkNetworkStatus();

    // Only check API if network appears to be online
    let apiStatus = false;
    if (networkStatus.online) {
      apiStatus = await checkApiConnectivity();
    }

    // Send combined status to renderer
    win.webContents.send("network-status-change", {
      ...networkStatus,
      api: apiStatus,
    });

    console.log("Network monitoring check:", {
      network: networkStatus.online,
      dns: networkStatus.details.dns,
      internet: networkStatus.details.internet,
      api: apiStatus,
    });
  };

  // Set up periodic checking - every 60 seconds
  networkCheckInterval = setInterval(checkConnectivity, 60000);

  // Run an initial combined check
  checkConnectivity();
}

function createMainWindow() {
  mainWindow = new BrowserWindow({
    width: 1800,
    height: 1200,
    frame: false,
    fullscreen: true,
    minWidth: 1200,
    minHeight: 750,
    webPreferences: {
      preload: getPreloadPath(),
      nodeIntegration: false,
      contextIsolation: true,
      sandbox: true,
      webSecurity: false,
    },
  });

  mainWindow.setMenuBarVisibility(false);

  if (isDev()) {
    mainWindow.loadURL("https://localhost:5123");
    mainWindow.webContents.openDevTools();
  } else {
    mainWindow.loadFile(path.join(app.getAppPath(), "/dist-react/index.html"), {
      hash: "login",
    });
    checkForUpdates();
  }

  // Set up network monitoring
  setupNetworkMonitoring(mainWindow);

  return mainWindow;
}

async function checkForUpdates() {
  if (!isDev()) {
    try {
      await autoUpdater.checkForUpdates();
    } catch (err) {
      log.error("Error checking for updates:", err);
    }
  }
}

// Function to scan the local network for servers on port 5823
async function scanNetworkForServers(port = 5823) {
  try {
    // Get local IP address to determine network range
    const interfaces = networkInterfaces();
    let localIp = "";
    let networkPrefix = "";

    // Find the active network interface with an IPv4 address
    Object.keys(interfaces).forEach((ifName) => {
      interfaces[ifName]?.forEach((iface) => {
        if (iface.family === "IPv4" && !iface.internal) {
          localIp = iface.address;
        }
      });
    });

    if (!localIp) {
      console.error("Could not determine local IP address");
      return { success: false, error: "Could not determine local IP address" };
    }

    // Extract network prefix (e.g., 192.168.1)
    networkPrefix = localIp.split(".").slice(0, 3).join(".");

    console.log(
      `Scanning network ${networkPrefix}.* for servers on port ${port}`
    );

    const foundServers: string[] = [];
    const scanPromises = [];

    // Scan IP range from .1 to .254
    for (let i = 1; i <= 254; i++) {
      const ip = `${networkPrefix}.${i}`;

      // Skip own IP to save time
      if (ip === localIp) continue;

      scanPromises.push(
        new Promise<void>((resolve) => {
          const socket = new netsh.Socket();
          let resolved = false;

          // Set a timeout for the connection attempt
          socket.setTimeout(500);

          socket.on("connect", () => {
            if (!resolved) {
              foundServers.push(ip);
              socket.destroy();
              resolved = true;
              resolve();
            }
          });

          socket.on("timeout", () => {
            if (!resolved) {
              socket.destroy();
              resolved = true;
              resolve();
            }
          });

          socket.on("error", () => {
            if (!resolved) {
              socket.destroy();
              resolved = true;
              resolve();
            }
          });

          socket.connect(port, ip);
        })
      );
    }

    // Wait for all scan promises to complete
    await Promise.all(scanPromises);

    console.log(
      `Found ${foundServers.length} potential servers:`,
      foundServers
    );

    // Verify each server by making a GraphQL request
    const verifiedServers = [];

    for (const ip of foundServers) {
      try {
        const url = `https://${ip}:${port}/graphql`;
        const response = await axios({
          url,
          method: "POST",
          data: { query: "{ __typename }" },
          timeout: 2000,
          httpsAgent: new https.Agent({ rejectUnauthorized: false }),
        });

        if (response.status === 200 && response.data) {
          verifiedServers.push(url);
        }
      } catch (error) {
        // Try HTTP if HTTPS fails
        try {
          const url = `http://${ip}:${port}/graphql`;
          const response = await axios({
            url,
            method: "POST",
            data: { query: "{ __typename }" },
            timeout: 2000,
          });

          if (response.status === 200 && response.data) {
            verifiedServers.push(url);
          }
        } catch (error) {
          // Skip this server if both HTTPS and HTTP fail
          continue;
        }
      }
    }

    return { success: true, servers: verifiedServers };
  } catch (error: any) {
    console.error("Error scanning network:", error);
    return { success: false, error: error.message };
  }
}

// Function to save discovered server URL
async function saveServerUrl(url: string) {
  const folderPath = path.join(app.getPath("documents"), "Talisia");
  const filePath = path.join(folderPath, "server_url_config.json");

  try {
    if (!fs.existsSync(folderPath)) {
      fs.mkdirSync(folderPath, { recursive: true });
    }

    fs.writeFileSync(
      filePath,
      JSON.stringify({ server_url: url }, null, 2),
      "utf8"
    );

    try {
      execSync(`attrib +h "${filePath}"`);
    } catch {
      /* empty */
    }

    return { success: true };
  } catch (error: any) {
    return { success: false, error: error.message };
  }
}

// Function to get saved server URL
function getServerUrl() {
  const filePath = path.join(
    app.getPath("documents"),
    "Talisia",
    "server_url_config.json"
  );

  try {
    if (!fs.existsSync(filePath)) {
      return { serverUrl: null };
    }

    const data = JSON.parse(fs.readFileSync(filePath, "utf8"));
    return { serverUrl: data.server_url };
  } catch (error: any) {
    return { serverUrl: null, error: error.message };
  }
}

async function startApi() {
  const apiPath = isDev()
    ? path.join(app.getAppPath(), "api", "start.bat")
    : path.join(process.resourcesPath, "api", "start.bat");

  return new Promise((resolve, reject) => {
    apiProcess = spawn(apiPath, [], {
      shell: true,
      detached: true,
      stdio: "ignore",
    });

    apiProcess.on("error", (err: any) => {
      console.error("Failed to start API:", err);
      reject(err);
    });

    // Wait a bit for the API to start
    setTimeout(resolve, 5000);
  });
}

async function stopApi() {
  const stopPath = isDev()
    ? path.join(app.getAppPath(), "api", "stop.bat")
    : path.join(process.resourcesPath, "api", "stop.bat");

  return new Promise((resolve, reject) => {
    exec(stopPath, (error) => {
      if (error) {
        console.error("Failed to stop API:", error);
        reject(error);
      } else {
        resolve(true);
      }
    });
  });
}

app.whenReady().then(async () => {
  try {
    await startApi();
    protocol.registerBufferProtocol("cachedimage", (request, callback) => {
      const fileName = request.url.replace("cachedimage://", "");
      const localPath = path.join(cacheDir, fileName);
      fs.readFile(localPath, (err, data) => {
        if (err) return callback({ error: -6 });
        const ext = path.extname(localPath).slice(1);
        const mimeType =
          ext === "png"
            ? "image/png"
            : ext === "gif"
            ? "image/gif"
            : "image/jpeg";
        callback({ mimeType, data });
      });
    });

    if (isDev()) {
      https
        .createServer(httpsOptions, (req, res) => {
          res.writeHead(200);
          res.end("HTTPS server running for Electron app.");
        })
        .listen(5123, () =>
          console.log("HTTPS server running on https://localhost:5123")
        );
    }

    createMainWindow();

    if (!isDev()) setInterval(checkForUpdates, 4 * 60 * 60 * 1000);

    session.defaultSession.setUserAgent(
      `${session.defaultSession.getUserAgent()} CustomAgent`
    );
    session.defaultSession.cookies.flushStore();

    session.defaultSession.webRequest.onBeforeSendHeaders(
      async (details, callback) => {
        try {
          const cookies = await session.defaultSession.cookies.get({
            url: "https://localhost:4000",
          });
          const hidCookie = cookies.find((cookie) => cookie.name === "hid");
          if (hidCookie)
            details.requestHeaders["Cookie"] = `hid=${hidCookie.value}`;
        } catch (error) {
          console.error("Failed to retrieve cookies:", error);
        }
        callback({ requestHeaders: details.requestHeaders });
      }
    );

    session.defaultSession.webRequest.onHeadersReceived((details, callback) => {
      if (details.responseHeaders) {
        const cspHeader =
          details.responseHeaders["Content-Security-Policy"] ||
          details.responseHeaders["content-security-policy"];
        if (cspHeader) {
          const newCsp = cspHeader.map((headerValue) => {
            let updated = headerValue;
            if (updated.includes("img-src")) {
              if (!updated.includes("blob:"))
                updated = updated.replace(/img-src\s+/, "img-src blob: ");
              if (!updated.includes("file:"))
                updated = updated.replace(/img-src\s+/, "img-src file: ");
              if (!updated.includes("cachedimage:"))
                updated = updated.replace(
                  /img-src\s+/,
                  "img-src cachedimage: "
                );
            }
            if (!updated.includes("eqzgvivfuzmyfxbupxht.supabase.co")) {
              updated = updated.replace(
                /connect-src\s+([^;]+)/,
                (_, p1) =>
                  `connect-src ${p1} https://eqzgvivfuzmyfxbupxht.supabase.co`
              );
            }
            return updated;
          });
          details.responseHeaders["Content-Security-Policy"] = newCsp;
          details.responseHeaders["content-security-policy"] = newCsp;
        }
      }
      callback({ responseHeaders: details.responseHeaders || {} });
    });

    session.defaultSession.webRequest.onBeforeRequest(
      {
        urls: [
          "https://eqzgvivfuzmyfxbupxht.supabase.co/storage/v1/object/public/heal/*",
        ],
      },
      (details, callback) => {
        const url = details.url;
        const localPath = getCacheFilePath(url);
        if (fs.existsSync(localPath)) {
          const fileName = path.basename(localPath);
          callback({ redirectURL: `cachedimage://${fileName}` });
        } else {
          cacheImage(url);
          callback({});
        }
      }
    );

    ipcMain.handle("clear-image-cache", async () => {
      try {
        if (fs.existsSync(cacheDir)) {
          const files = fs.readdirSync(cacheDir);
          for (const file of files) fs.unlinkSync(path.join(cacheDir, file));
          return { status: true };
        }
        return { status: false, error: "Cache directory does not exist." };
      } catch (err: any) {
        return { status: false, error: err.message };
      }
    });

    ipcMain.handle("save-company-id", async (_, companyId) => {
      const folderPath = path.join(app.getPath("documents"), "Talisia");
      const filePath = path.join(folderPath, "company_config.json");
      try {
        if (!fs.existsSync(folderPath))
          fs.mkdirSync(folderPath, { recursive: true });
        fs.writeFileSync(
          filePath,
          JSON.stringify({ company_id: companyId }, null, 2),
          "utf8"
        );
        try {
          execSync(`attrib +h "${filePath}"`);
        } catch {
          /* empty */
        }
        return { success: true };
      } catch (error: any) {
        return { success: false, error: error.message };
      }
    });

    ipcMain.handle("save-company-sub", async (_, company_sub) => {
      const folderPath = path.join(app.getPath("documents"), "Talisia");
      const filePath = path.join(folderPath, "company_config_sub.json");
      try {
        if (!fs.existsSync(folderPath))
          fs.mkdirSync(folderPath, { recursive: true });
        fs.writeFileSync(
          filePath,
          JSON.stringify({ company_sub: company_sub }, null, 2),
          "utf8"
        );
        try {
          execSync(`attrib +h "${filePath}"`);
        } catch {
          /* empty */
        }
        return { success: true };
      } catch (error: any) {
        return { success: false, error: error.message };
      }
    });

    ipcMain.handle("get-company-id", async () => {
      const filePath = path.join(
        app.getPath("documents"),
        "Talisia",
        "company_config.json"
      );
      try {
        if (!fs.existsSync(filePath)) return { companyId: null };
        const data = JSON.parse(fs.readFileSync(filePath, "utf8"));
        return { companyId: data.company_id };
      } catch (error: any) {
        return { companyId: null, error: error.message };
      }
    });

    ipcMain.handle("get-company-sub", async () => {
      const filePath = path.join(
        app.getPath("documents"),
        "Talisia",
        "company_config_sub.json"
      );
      try {
        if (!fs.existsSync(filePath)) return { company_sub: null };
        const data = JSON.parse(fs.readFileSync(filePath, "utf8"));
        return { company_sub: data.company_sub };
      } catch (error: any) {
        return { company_sub: null, error: error.message };
      }
    });

    ipcMain.handle("check-for-updates", async () => checkForUpdates());
    ipcMain.handle("start-update", async () => {
      if (!isDev()) autoUpdater.quitAndInstall(false, true);
    });

    ipcMain.handle("check-network-status", async () => {
      return checkNetworkStatus();
    });

    ipcMain.on("app-quit", () => app.quit());

    contextMenu({
      prepend: (defaultActions, parameters, browserWindow) => {
        // Different menu items based on development or production mode
        if (isDev()) {
          // Development mode menu
          return [
            {
              label: "Toggle Dev Tools",
              click: () => {
                if ("webContents" in browserWindow) {
                  browserWindow.webContents.toggleDevTools();
                } else if ("toggleDevTools" in browserWindow) {
                  browserWindow.toggleDevTools();
                }
              },
            },
            {
              label: "Reload Talisia POS",
              click: (_, focusedWindow) => {
                if (focusedWindow && "webContents" in focusedWindow) {
                  if (focusedWindow instanceof BrowserWindow) {
                    focusedWindow.webContents.reload();
                  }
                }
              },
            },
            {
              label: "Minimize Talisia POS",
              click: () => {
                if (browserWindow instanceof BrowserWindow) {
                  browserWindow.minimize();
                }
              },
            },
            { type: "separator" },
            {
              label: "Quit Talisia POS",
              click: () => app.quit(),
            },
          ];
        } else {
          // Production mode menu
          return [
            {
              label: "Minimize Talisia POS",
              click: () => {
                if (browserWindow instanceof BrowserWindow) {
                  browserWindow.minimize();
                }
              },
            },
            {
              label: "Reload Talisia POS",
              click: (_, focusedWindow) => {
                if (focusedWindow && "webContents" in focusedWindow) {
                  if (focusedWindow instanceof BrowserWindow) {
                    focusedWindow.webContents.reload();
                  }
                }
              },
            },
            { type: "separator" },
            {
              label: "Quit Talisia POS",
              click: () => app.quit(),
            },
          ];
        }
      },
      // Disable default inspect element option in production
      showInspectElement: isDev(),
      // Completely replace the default menu in production
      showCopyImage: isDev(),
      showSelectAll: isDev(),
      showCopyImageAddress: isDev(),
      showSaveImage: isDev(),
      showSaveImageAs: isDev(),
      showSaveLinkAs: isDev(),
      showSearchWithGoogle: isDev(),
      showCopyLink: isDev(),
    });

    // Add these new handlers for deployment type
    ipcMain.handle("save-deployment-type", async (_, isServer) => {
      const folderPath = path.join(app.getPath("documents"), "Talisia");
      const filePath = path.join(folderPath, "deployment_config.json");
      try {
        if (!fs.existsSync(folderPath))
          fs.mkdirSync(folderPath, { recursive: true });
        fs.writeFileSync(
          filePath,
          JSON.stringify({ is_server: isServer }, null, 2),
          "utf8"
        );
        try {
          execSync(`attrib +h "${filePath}"`);
        } catch {
          /* empty */
        }
        return { success: true };
      } catch (error: any) {
        return { success: false, error: error.message };
      }
    });

    ipcMain.handle("get-deployment-type", async () => {
      const filePath = path.join(
        app.getPath("documents"),
        "Talisia",
        "deployment_config.json"
      );
      try {
        if (!fs.existsSync(filePath)) return { isServer: null };
        const data = JSON.parse(fs.readFileSync(filePath, "utf8"));
        return { isServer: data.is_server };
      } catch (error: any) {
        return { isServer: null, error: error.message };
      }
    });

    // Add these new handlers for LAN server discovery
    ipcMain.handle("scan-network-for-servers", async () => {
      return scanNetworkForServers();
    });

    ipcMain.handle("save-server-url", async (_, url) => {
      return saveServerUrl(url);
    });

    ipcMain.handle("get-server-url", async () => {
      return getServerUrl();
    });
  } catch (error) {
    console.error("Failed to start API:", error);
    app.quit();
  }
});

app.on("window-all-closed", () => {
  if (process.platform !== "darwin") app.quit();
});

app.on("activate", () => {
  if (mainWindow === null) createMainWindow();
});

app.on(
  "certificate-error",
  (event, webContents, url, error, certificate, callback) => {
    if (url.startsWith("https://localhost")) {
      event.preventDefault();
      callback(true);
    } else {
      callback(false);
    }
  }
);

app.on("before-quit", async () => {
  if (networkCheckInterval) {
    clearInterval(networkCheckInterval);
  }

  try {
    await stopApi();
  } catch (error) {
    console.error("Failed to stop API:", error);
  }
});
