import React, { useContext, useState } from "react";
import { Box, Button, Flex, Grid } from "@chakra-ui/react";
import { Route, Switch, useHistory } from "react-router-dom";
import { DesktopRoute, desktopRoutes } from "../routesDesktop";
import TopDesktopNavBar from "../components/Desktop/TopNavBar";
import Sidebar from "../components/Desktop/Sidebar";
import Footer from "../components/Desktop/Footer";
import { MeContext } from "../components/Wrapper";
import { useLogoutMutation } from "../generated/graphql";

const DesktopLayout = () => {
  const history = useHistory();
  const [isSidebarOpen, setSidebarOpen] = useState(true);
  const navbarHeight = 50; // Fixed navbar height in pixels
  const [footerHeight, setFooterHeight] = useState(0); // Dynamic footer height
  const me = useContext(MeContext);
  const [, logout] = useLogoutMutation();

  const logOut = async () => {
    try {
      await logout({});
      history.push("/sign-in");
      console.log("Logged out successfully");
    } catch (error) {
      history.push("/sign-in");
      console.error("Error during logout:", error);
    }
  };

  const toggleSidebar = () => {
    setSidebarOpen(!isSidebarOpen);
  };

  // Permission mapping for routes
  const permissionMap: { [key: string]: string } = {
    "/POS": "POS",
    "/counter": "Counters",
    "/counters": "Counters",
    "/add-counter": "Counters",
    "/edit-counter": "Counters",
    "/import-product": "Import",
    "/transfer-stock": "Transfer",
    "/open-tabs": "Open Tabs",
    "/sales": "Sales",
    "/employee": "Users",
    "/update-profile": "Users",
    "/add-employee": "Users",
    "/employees": "Users",
    "/roles": "Designations",
    "/categories": "Categories",
    "/products": "Products",
    "/product": "Products",
    "/manage-product": "Products",
    "/pending-orders": "Pending Orders",
    "/customer-tag": "Customer Tags",
    "/expenses": "Expenses",
    "/troubleshoot": "Troubleshoot",
  };

  // Filter routes based on user's permissions
  const filteredRoutes = desktopRoutes
    .filter((route) => route.layout === "/admin" || route.layout === "/desktop")
    .filter((route) => {
      if (!me?.permissions || me.permissions.length === 0) return false;

      // Administrator can see all routes
      // if (me.permissions.some((perm) => perm.name === "Administrator")) {
      //   return true;
      // }

      const requiredPermission = permissionMap[route.path || ""];
      return requiredPermission
        ? me.permissions.some((perm) => perm.name === requiredPermission)
        : true; // If no permission is mapped, allow access by default
    });

  const renderRoutes = (routes: DesktopRoute[]) =>
    routes.map((prop: DesktopRoute, key: number) => {
      if (prop.category === "collapse" && prop.views) {
        return renderRoutes(prop.views);
      }
      if (prop.layout === "/desktop") {
        return (
          <Route
            path={prop.layout + prop.path}
            component={prop.component}
            key={key}
          />
        );
      }
      return null;
    });

  // If no permissions or no matching routes, show access denied
  if (!me?.permissions || filteredRoutes.length === 0) {
    return (
      <Flex direction="column" h="100vh" justify="center" align="center">
        <Box textAlign="center">
          <h1>Access Denied</h1>
          <p>You don't have permission to view this page.</p>
        </Box>
        <Box mt={4}>
          <Button colorScheme="blue" onClick={logOut}>
            Logout
          </Button>
        </Box>
      </Flex>
    );
  }

  return (
    <Flex direction="column" h="100vh">
      {/* Top Navbar */}
      <TopDesktopNavBar
        onToggleSidebar={toggleSidebar}
        height={`${navbarHeight}px`}
      />

      {/* Main Content Area */}
      <Flex flex="1" minH={0}>
        {/* Sidebar */}
        {isSidebarOpen && (
          <Sidebar
            routes={filteredRoutes}
            navbarHeight={navbarHeight}
            footerHeight={footerHeight}
          />
        )}
        {/* Main Content */}
        <Box
          flex="1"
          ml={isSidebarOpen ? "250px" : "0"}
          h={`calc(104% - ${navbarHeight + footerHeight}px)`}
          overflow="hidden"
        >
          <Grid p={2} h="100%">
            <Switch>{renderRoutes(filteredRoutes as DesktopRoute[])}</Switch>
          </Grid>
        </Box>
      </Flex>

      {/* Footer */}
      <Footer onHeightChange={setFooterHeight} />
    </Flex>
  );
};

export default DesktopLayout;
