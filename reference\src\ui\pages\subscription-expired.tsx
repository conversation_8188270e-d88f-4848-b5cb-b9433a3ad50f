import { Box, Text, Button, Flex, Heading, VStack } from "@chakra-ui/react";
import { useHistory } from "react-router-dom";
import { useLogoutMutation } from "../generated/graphql";

const SubscriptionExpiredPage = () => {
  const history = useHistory();
  const [, logout] = useLogoutMutation();

  const handleLogout = async () => {
    try {
      await logout({});
      history.push("/login");
    } catch (error) {
      history.push("/login");
      console.error("Error during logout:", error);
    }
  };

  return (
    <Flex
      height="100vh"
      alignItems="center"
      justifyContent="center"
      bg="gray.50"
    >
      <Box
        textAlign="center"
        p={8}
        maxW="md"
        borderWidth={1}
        borderRadius="lg"
        boxShadow="lg"
        bg="white"
      >
        <VStack spacing={6}>
          <Heading as="h2" size="xl" color="red.500">
            Subscription Expired
          </Heading>
          <Text fontSize="lg">
            Your company's subscription has expired. Please contact your
            administrator or renew your subscription to continue using the
            application.
          </Text>
          <Button
            colorScheme="blue"
            size="lg"
            onClick={handleLogout}
            width="full"
          >
            Logout
          </Button>
        </VStack>
      </Box>
    </Flex>
  );
};

export default SubscriptionExpiredPage;
