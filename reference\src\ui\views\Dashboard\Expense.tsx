// src/pages/ManageExpenses.tsx
import React, { useContext, useState } from "react";
import {
  Box,
  Button,
  Flex,
  Heading,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalCloseButton,
  FormControl,
  FormLabel,
  Input,
  Select,
  useDisclosure,
  useToast,
  VStack,
  Spinner,
  Center,
  Text,
  Card,
  CardBody,
  HStack,
  IconButton,
  Tooltip,
  Badge,
  useColorModeValue,
} from "@chakra-ui/react";
import { format, startOfDay } from "date-fns";
import { FiEdit2, FiTrash2, FiCheckCircle, FiFilter } from "react-icons/fi";
import {
  useAddExpenseMutation,
  useAuthorizeExpenseMutation,
  useDeleteExpenseMutation,
  useEditExpenseMutation,
  useExpensesQuery,
} from "../../generated/graphql";
import { formatToMoney, toDateTime } from "../../utils/Helpers";
import { MeContext } from "../../components/Wrapper";
import { hasPermission } from "../../interfaces/Helpers";

// Define the interface for our component's internal use
interface Expense {
  id: number;
  expenseDate: string;
  title: string;
  details: string;
  amount: number;
  type: "credit" | "debit";
  status: "requested" | "approved";
  assetType: "office" | "employee" | "other";
  authorizer: { id: number };
  requester: { id: number };
}

const ManageExpenses = () => {
  const [selectedExpense, setSelectedExpense] = useState<Expense | null>(null);
  const [filter, setFilter] = useState({
    startDate: format(startOfDay(new Date()), "yyyy-MM-dd"),
    endDate: format(startOfDay(new Date()), "yyyy-MM-dd"),
  });
  const me = useContext(MeContext);

  const { isOpen, onOpen, onClose } = useDisclosure();
  const toast = useToast({ position: "top", duration: 3000 });
  const borderColor = useColorModeValue("gray.200", "gray.700");

  // Queries and Mutations
  const [{ data, fetching: loading }, refetch] = useExpensesQuery({
    variables: { filter: filter ? filter : null },
    requestPolicy: "network-only",
  });
  const [, addExpense] = useAddExpenseMutation();
  const [, editExpense] = useEditExpenseMutation();
  const [, deleteExpense] = useDeleteExpenseMutation();
  const [, authorizeExpense] = useAuthorizeExpenseMutation();

  // Form state
  const [formData, setFormData] = useState({
    expenseDate: new Date(),
    title: "",
    details: "",
    amount: 0,
    type: "credit",
    assetType: "other",
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      const input = {
        ...formData,
        expenseDate: formData.expenseDate.toISOString(),
        amount: Number(formData.amount),
      };
      if (selectedExpense) {
        await editExpense({ id: selectedExpense.id, args: input });
        toast({
          title: "Expense Updated",
          status: "success",
          duration: 3000,
          position: "top-right",
        });
      } else {
        const result = await addExpense({ args: input });
        if (result.error || result.data?.addExpense.error) {
          toast({
            title: "Error adding expense",
            status: "error",
            duration: 3000,
            position: "top-right",
          });
          return;
        }
        toast({
          title: "Expense Added",
          status: "success",
          duration: 3000,
          position: "top-right",
        });
      }
      onClose();
      resetForm();
    } catch (error) {
      toast({
        title: "Error",
        description: (error as Error).message,
        status: "error",
        duration: 3000,
        position: "top-right",
      });
    }
  };

  const handleAuthorize = async (id: number) => {
    try {
      const result = await authorizeExpense({ id });
      if (result.error || result.data?.authorizeExpense.error) {
        toast({
          title: "Error authorizing expense",
          status: "error",
          duration: 3000,
        });
        return;
      }
      if (result?.data?.authorizeExpense?.status) {
        toast({
          title: "Expense Authorized",
          status: "success",
          duration: 3000,
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: (error as Error).message,
        status: "error",
        duration: 3000,
      });
    }
  };

  const handleDelete = async (id: number) => {
    if (window.confirm("Are you sure you want to delete this expense?")) {
      try {
        const result = await deleteExpense({ id });
        if (result.error || result.data?.deleteExpense.error) {
          toast({
            title: "Error deleting expense",
            status: "error",
            duration: 3000,
          });
          return;
        }
        if (result?.data?.deleteExpense?.status) {
          toast({
            title: "Expense Deleted",
            status: "success",
            duration: 3000,
          });
        }
      } catch (error) {
        toast({
          title: "Error",
          description: (error as Error).message,
          status: "error",
          duration: 3000,
        });
      }
    }
  };

  const resetForm = () => {
    setSelectedExpense(null);
    setFormData({
      expenseDate: new Date(),
      title: "",
      details: "",
      amount: 0,
      type: "credit",
      assetType: "other",
    });
  };

  return (
    <Box p={6} maxW="container.xl" mx="auto">
      <Card mb={6} boxShadow="lg" borderRadius="xl">
        <CardBody>
          <Flex justify="space-between" align="center" mb={6}>
            <Heading size="lg" color="blue.600">
              Expense Management
            </Heading>
            <Button
              colorScheme="blue"
              onClick={() => {
                resetForm();
                onOpen();
              }}
              size="lg"
              leftIcon={<FiEdit2 />}
            >
              Add New Expense
            </Button>
          </Flex>

          {/* Filter Section */}
          <Card variant="outline" mb={6} borderColor={borderColor}>
            <CardBody>
              <Flex gap={4} align="center">
                <HStack flex={1}>
                  <FormControl>
                    <FormLabel>Start Date</FormLabel>
                    <Input
                      type="date"
                      value={filter.startDate}
                      onChange={(e) =>
                        setFilter({ ...filter, startDate: e.target.value })
                      }
                      size="md"
                    />
                  </FormControl>
                  <FormControl>
                    <FormLabel>End Date</FormLabel>
                    <Input
                      type="date"
                      value={filter.endDate}
                      onChange={(e) =>
                        setFilter({ ...filter, endDate: e.target.value })
                      }
                      size="md"
                    />
                  </FormControl>
                </HStack>
                <Button
                  colorScheme="blue"
                  onClick={() => refetch()}
                  leftIcon={<FiFilter />}
                  size="lg"
                  mt={8}
                >
                  Apply Filter
                </Button>
              </Flex>
            </CardBody>
          </Card>

          {/* Expenses Table */}
          <Box overflowX="auto">
            <Table variant="simple" size="lg">
              <Thead>
                <Tr>
                  <Th>Date</Th>
                  <Th>Title</Th>
                  <Th>Amount</Th>
                  <Th>Status</Th>
                  <Th>Actions</Th>
                </Tr>
              </Thead>
              <Tbody>
                {loading ? (
                  <Tr>
                    <Td colSpan={6}>
                      <Center py={6}>
                        <VStack spacing={3}>
                          <Spinner
                            thickness="4px"
                            speed="0.65s"
                            emptyColor="gray.200"
                            color="blue.500"
                            size="xl"
                          />
                          <Text color="gray.500" fontWeight="medium">
                            Loading expenses...
                          </Text>
                        </VStack>
                      </Center>
                    </Td>
                  </Tr>
                ) : (
                  data?.expenses.map((expense) => {
                    // Get today's date without time
                    const today = startOfDay(new Date());

                    // Compare with expense.expenseDate after converting it to start of the day
                    const isToday =
                      format(
                        startOfDay(new Date(Number(expense.expenseDate))),
                        "yyyy-MM-dd"
                      ) === format(today, "yyyy-MM-dd");

                    // Check permissions
                    const hasPermissionToView = hasPermission(me?.permissions, [
                      "Expenses>All",
                      "Expenses>Approve",
                    ]);
                    const hasPermissionToApprove = hasPermission(
                      me?.permissions,
                      ["Expenses>Approve"]
                    );

                    const shouldShow = isToday || hasPermissionToView;
                    return (
                      <Tr key={expense.id} _hover={{ bg: "gray.50" }}>
                        <Td>{toDateTime(Number(expense.expenseDate))}</Td>
                        <Td fontWeight="medium">{expense.title}</Td>
                        <Td>
                          <Badge
                            colorScheme={
                              expense.type === "credit" ? "green" : "red"
                            }
                            fontSize="md"
                            px={3}
                            py={1}
                            borderRadius="full"
                          >
                            {formatToMoney(expense.amount)}
                          </Badge>
                        </Td>
                        <Td>
                          <Badge
                            colorScheme={
                              expense.status === "approved" ? "green" : "yellow"
                            }
                            variant="subtle"
                          >
                            {expense.status}
                          </Badge>
                        </Td>
                        <Td>
                          <HStack spacing={2}>
                            {shouldShow && (
                              <Tooltip label="Edit Expense">
                                <IconButton
                                  aria-label="Edit"
                                  icon={<FiEdit2 />}
                                  onClick={() => {
                                    setSelectedExpense({
                                      id: expense.id,
                                      expenseDate: expense.expenseDate,
                                      title: expense.title,
                                      details: expense.details,
                                      amount: expense.amount,
                                      type: expense.type as "credit" | "debit",
                                      status: expense.status as
                                        | "requested"
                                        | "approved",
                                      assetType: expense.assetType as
                                        | "office"
                                        | "employee"
                                        | "other",
                                      authorizer: {
                                        id: expense.authorizer?.id || 0,
                                      },
                                      requester: {
                                        id: expense.requester?.id || 0,
                                      },
                                    });
                                    setFormData({
                                      expenseDate: new Date(
                                        Number(expense.expenseDate)
                                      ),
                                      title: expense.title,
                                      details: expense.details,
                                      amount: expense.amount,
                                      type: expense.type as "credit" | "debit",
                                      assetType: expense.assetType as
                                        | "office"
                                        | "employee"
                                        | "other",
                                    });
                                    onOpen();
                                  }}
                                  colorScheme="blue"
                                  variant="ghost"
                                />
                              </Tooltip>
                            )}
                            {hasPermissionToApprove && (
                              <Tooltip label="Authorize Expense">
                                <IconButton
                                  aria-label="Authorize"
                                  icon={<FiCheckCircle />}
                                  onClick={() => handleAuthorize(expense.id)}
                                  isDisabled={expense.status === "approved"}
                                  colorScheme="green"
                                  variant="ghost"
                                />
                              </Tooltip>
                            )}
                            {shouldShow && (
                              <Tooltip label="Delete Expense">
                                <IconButton
                                  aria-label="Delete"
                                  icon={<FiTrash2 />}
                                  onClick={() => handleDelete(expense.id)}
                                  colorScheme="red"
                                  variant="ghost"
                                />
                              </Tooltip>
                            )}
                          </HStack>
                        </Td>
                      </Tr>
                    );
                  })
                )}
              </Tbody>
            </Table>
          </Box>
        </CardBody>
      </Card>

      {/* Add/Edit Modal */}
      <Modal isOpen={isOpen} onClose={onClose} size="xl">
        <ModalOverlay backdropFilter="blur(10px)" />
        <ModalContent>
          <ModalHeader fontSize="2xl" fontWeight="bold">
            {selectedExpense ? "Edit Expense" : "Add New Expense"}
          </ModalHeader>
          <ModalCloseButton />
          <ModalBody pb={6}>
            <form onSubmit={handleSubmit}>
              <VStack spacing={6}>
                <FormControl>
                  <FormLabel>Date</FormLabel>
                  <Input
                    type="date"
                    value={
                      formData.expenseDate instanceof Date &&
                      !isNaN(formData.expenseDate.getTime())
                        ? format(formData.expenseDate, "yyyy-MM-dd")
                        : ""
                    }
                    onChange={(e) =>
                      setFormData({
                        ...formData,
                        expenseDate: new Date(e.target.value),
                      })
                    }
                    required
                    size="lg"
                  />
                </FormControl>
                <FormControl>
                  <FormLabel>Title</FormLabel>
                  <Input
                    value={formData.title}
                    onChange={(e) =>
                      setFormData({ ...formData, title: e.target.value })
                    }
                    required
                    size="lg"
                    placeholder="Enter expense title"
                  />
                </FormControl>
                <FormControl>
                  <FormLabel>Details</FormLabel>
                  <Input
                    value={formData.details}
                    onChange={(e) =>
                      setFormData({ ...formData, details: e.target.value })
                    }
                    required
                    size="lg"
                    placeholder="Enter expense details"
                  />
                </FormControl>
                <FormControl>
                  <FormLabel>Amount</FormLabel>
                  <Input
                    type="number"
                    value={formData.amount}
                    onChange={(e) =>
                      setFormData({
                        ...formData,
                        amount: Number(e.target.value),
                      })
                    }
                    required
                    size="lg"
                    placeholder="0.00"
                  />
                </FormControl>
                <FormControl flex={1}>
                  <FormLabel>Asset Type</FormLabel>
                  <Select
                    value={formData.assetType}
                    onChange={(e) =>
                      setFormData({
                        ...formData,
                        assetType: e.target.value as
                          | "office"
                          | "employee"
                          | "other",
                      })
                    }
                    size="lg"
                  >
                    <option value="office">Office</option>
                    <option value="employee">Employee</option>
                    <option value="other">Other</option>
                  </Select>
                </FormControl>
                <Button
                  type="submit"
                  colorScheme="blue"
                  width="full"
                  size="lg"
                  mt={4}
                >
                  {selectedExpense ? "Update Expense" : "Add Expense"}
                </Button>
              </VStack>
            </form>
          </ModalBody>
        </ModalContent>
      </Modal>
    </Box>
  );
};

export default ManageExpenses;
