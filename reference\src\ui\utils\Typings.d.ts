/* eslint-disable @typescript-eslint/no-explicit-any */
declare module "*.scss";
declare module "*.css";
declare module "*.js";
// src/global.d.ts

interface ElectronAPI {
  send: (channel: string, data: any) => void;
  on: (
    channel: string,
    callback: (event: Electron.IpcRendererEvent, ...args: any[]) => void
  ) => void;
  invoke: (channel: string, data: any) => Promise<any>;
}

declare global {
  interface Window {
    electron: ElectronAPI; // Declare the `electron` object on the global `window` object
  }
}

export {};
