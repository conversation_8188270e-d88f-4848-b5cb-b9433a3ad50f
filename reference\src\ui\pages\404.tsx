import { Box, Text, Button, Flex } from "@chakra-ui/react";
import NotFoundIllustration from "../components/Icons/NotFound";
import { useHistory } from "react-router-dom";

const NotFoundPage = () => {
  const navigate = useHistory();
  return (
    <Flex height="100vh" alignItems="center" justifyContent="center">
      <Box textAlign="center">
        <NotFoundIllustration
          style={{ margin: "auto" }}
          height={200}
          width={200}
        />
        <Text fontSize="4xl" fontWeight="bold" mt={8}>
          Page Not Found
        </Text>
        <Text fontSize="lg" mt={4}>
          The page you're looking for doesn't exist.
        </Text>
        <Button
          colorScheme="blue"
          variant="outline"
          mt={8}
          onClick={() => navigate.push("/")}
        >
          Go Back
        </Button>
      </Box>
    </Flex>
  );
};

export default NotFoundPage;
