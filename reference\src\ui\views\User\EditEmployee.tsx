/* eslint-disable @typescript-eslint/no-non-null-asserted-optional-chain */
/* eslint-disable @typescript-eslint/no-unused-vars */
import React, { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import {
  Flex,
  Box,
  Button,
  FormControl,
  FormLabel,
  Input,
  Text,
  useColorModeValue,
  FormErrorMessage,
  useToast,
  Grid,
  GridItem,
} from "@chakra-ui/react";
import { AsyncSelect, SingleValue, ActionMeta } from "chakra-react-select";
import { useHistory, useLocation } from "react-router-dom";
import {
  useChangeEmployeeStatusMutation,
  useChangeEmployeeRoleMutation,
  useGetRolesQuery,
  useGetDepartmentsQuery,
  User,
  useGetStoresQuery,
  useAssignStoreKeeperMutation,
} from "../../generated/graphql";

interface IStatusForm {
  status: string;
}

interface IRoleForm {
  role: number;
  designation: string;
  department: number;
}

interface IStoreForm {
  storeId: number;
}

interface PropState {
  user: User;
}

// Constant list of status options
const statusOptions = [
  { label: "DISABLED", value: "DISABLED" },
  { label: "NEW", value: "NEW" },
  { label: "ACTIVE", value: "ACTIVE" },
  { label: "LOCKED", value: "LOCKED" },
  { label: "DELETED", value: "DELETED" },
  { label: "EXPIRED", value: "EXPIRED" },
  { label: "EXPIRING", value: "EXPIRING" },
  { label: "ONLEAVE", value: "ONLEAVE" },
  { label: "SUSPENDED", value: "SUSPENDED" },
  { label: "MATERNITY", value: "MATERNITY" },
  { label: "PATERNITY", value: "PATERNITY" },
  { label: "SABBATICAL", value: "SABBATICAL" },
  { label: "TERMINATED", value: "TERMINATED" },
];

const EditEmployee: React.FC = () => {
  const toast = useToast({ position: "top" });
  const state = useLocation().state as PropState;
  const employee = state.user;
  console.log("our received employee to edit is: ", employee);

  // Form hooks for separate forms
  const {
    register: registerStatus,
    handleSubmit: handleSubmitStatus,
    setValue: setValueStatus,
    formState: { errors: errorsStatus, isSubmitting: isSubmittingStatus },
    reset: resetStatus,
  } = useForm<IStatusForm>();

  const {
    register: registerRole,
    handleSubmit: handleSubmitRole,
    setValue: setValueRole,
    formState: { errors: errorsRole, isSubmitting: isSubmittingRole },
    reset: resetRole,
  } = useForm<IRoleForm>();

  const {
    register: registerStore,
    handleSubmit: handleSubmitStore,
    setValue: setValueStore,
    formState: { errors: errorsStore, isSubmitting: isSubmittingStore },
    reset: resetStore,
  } = useForm<IStoreForm>();

  const [error, setError] = useState("");

  // GraphQL queries for roles and departments
  const [{ data: rolesData, fetching: loadingRoles }] = useGetRolesQuery();
  const [{ data: departmentsData, fetching: loadingDepartments }] =
    useGetDepartmentsQuery();

  // Local state for select options
  const [rolesOptions, setRolesOptions] = useState<
    { label: string; value: number }[]
  >([]);
  const [departmentOptions, setDepartmentOptions] = useState<
    { label: string; value: number }[]
  >([]);

  // Update rolesOptions from query
  useEffect(() => {
    if (rolesData && !loadingRoles) {
      const opts = rolesData.getRoles
        .filter((r) => r.sys === false)
        .map((r) => ({ label: r.name, value: r.id }));
      setRolesOptions(opts);
    }
  }, [rolesData, loadingRoles]);

  // Update departmentOptions from query
  useEffect(() => {
    if (departmentsData && !loadingDepartments) {
      const opts = departmentsData.getDepartments.map((d) => ({
        label: d.name,
        value: d.id,
      }));
      setDepartmentOptions(opts);
    }
  }, [departmentsData, loadingDepartments]);

  // Prepopulate both forms with the employee's current values
  useEffect(() => {
    if (employee) {
      // For status form
      setValueStatus("status", employee.employee?.status!);
      // For role form
      setValueRole("role", employee.employee?.roleId!);
      setValueRole("designation", employee.employee?.designation!);
      setValueRole("department", employee.employee?.departmentId!);
    }
  }, [employee, setValueStatus, setValueRole]);

  // GraphQL mutations
  const [, changeEmployeeStatusMutation] = useChangeEmployeeStatusMutation();
  const [, changeEmployeeRoleMutation] = useChangeEmployeeRoleMutation();
  const [, assignStoreKeeper] = useAssignStoreKeeperMutation();

  // Handlers for form submission
  async function onSubmitStatus(values: IStatusForm) {
    setError("");
    const response = await changeEmployeeStatusMutation({
      employeeId: employee.employee?.id!,
      status: values.status,
    });
    if (!response.data?.changeEmployeeStatus.status) {
      toast({
        title: "Failed to change employee status",
        status: "error",
        isClosable: true,
      });
      return;
    }
    toast({
      title: "Employee status updated successfully",
      status: "success",
      isClosable: true,
    });
    // Optionally, you can reset the form or update local state.
  }

  async function onSubmitRole(values: IRoleForm) {
    setError("");
    const response = await changeEmployeeRoleMutation({
      employeeId: employee.employee?.id!,
      companyRole: values.role,
      departmentId: values.department,
      designation: values.designation,
    });
    if (!response.data?.changeEmployeeRole.status) {
      toast({
        title: "Failed to update employee role",
        status: "error",
        isClosable: true,
      });
      return;
    }
    toast({
      title: "Employee role updated successfully",
      status: "success",
      isClosable: true,
    });
    resetRole();
  }

  async function onSubmitStore(values: IStoreForm) {
    setError("");
    const response = await assignStoreKeeper({
      storeId: values.storeId,
      userId: employee.id,
    });

    if (!response.data?.assignStoreKeeper.status) {
      toast({
        title: "Failed to assign store",
        description:
          response.data?.assignStoreKeeper.error?.message ||
          "Unknown error occurred",
        status: "error",
        isClosable: true,
      });
      return;
    }

    toast({
      title: "Store assigned successfully",
      status: "success",
      isClosable: true,
    });
    resetStore();
  }

  // Handlers for AsyncSelect changes
  function handleStatusChange(
    newValue: SingleValue<{ label: string; value: string }>,
    actionMeta: ActionMeta<{ label: string; value: string }>
  ): void {
    setValueStatus("status", newValue!.value);
  }

  function handleRoleChange(
    newValue: SingleValue<{ label: string; value: number }>,
    actionMeta: ActionMeta<{ label: string; value: number }>
  ): void {
    setValueRole("role", newValue!.value);
  }

  function handleDepartmentChange(
    newValue: SingleValue<{ label: string; value: number }>,
    actionMeta: ActionMeta<{ label: string; value: number }>
  ): void {
    setValueRole("department", newValue!.value);
  }

  function handleStoreChange(
    newValue: SingleValue<{ label: string; value: number }>,
    actionMeta: ActionMeta<{ label: string; value: number }>
  ): void {
    setValueStore("storeId", newValue!.value);
  }

  const textColor = useColorModeValue("gray.700", "white");
  const bgCard = useColorModeValue("white", "gray.800");

  // Add store options state
  const [storeOptions, setStoreOptions] = useState<
    { label: string; value: number }[]
  >([]);
  const [{ data: storesData, fetching: loadingStores }, getStores] =
    useGetStoresQuery();

  // Add this effect to update store options
  useEffect(() => {
    if (storesData && !loadingStores) {
      const opts = storesData.getStores.map((store) => ({
        label: store.name,
        value: store.id,
      }));
      setStoreOptions(opts);
    }
  }, [storesData, loadingStores]);

  const loadStores = async (searchInput: string) => {
    if (searchInput.length > 0) {
      if (!loadingStores && storesData) {
        return storesData.getStores
          .filter((item) =>
            item.name
              .toLocaleLowerCase()
              .includes(searchInput.toLocaleLowerCase())
          )
          .map((r) => ({
            label: r.name,
            value: r.id,
          }));
      }
      await getStores({ requestPolicy: "network-only" });
      if (storesData) {
        return storesData.getStores
          .filter((item) =>
            item.name
              .toLocaleLowerCase()
              .includes(searchInput.toLocaleLowerCase())
          )
          .map((r) => ({
            label: r.name,
            value: r.id,
          }));
      }
    } else {
      if (!loadingStores && storesData)
        return storesData.getStores.map((r) => ({
          label: r.name,
          value: r.id,
        }));
    }
    return [];
  };

  return (
    <Flex
      direction="column"
      p="24px"
      bg={bgCard}
      borderRadius="md"
      boxShadow="lg"
      maxW="900px"
      mx="auto"
    >
      <Text
        fontSize="3xl"
        fontWeight="bold"
        mb="8"
        color={textColor}
        textAlign="center"
      >
        Edit Employee
      </Text>

      {/* Change Status Form */}
      <Box
        p="6"
        mb="8"
        borderWidth="1px"
        borderRadius="md"
        boxShadow="md"
        bg={useColorModeValue("gray.50", "gray.700")}
      >
        <Text fontSize="xl" fontWeight="semibold" mb="4" textAlign="center">
          Change Employee Status
        </Text>
        <form onSubmit={handleSubmitStatus(onSubmitStatus)}>
          <FormControl mb="4" isInvalid={!!errorsStatus.status}>
            <FormLabel>Status</FormLabel>
            <AsyncSelect
              variant="flushed"
              name="status"
              defaultOptions={statusOptions}
              options={statusOptions}
              isSearchable
              placeholder="Select Status"
              onChange={handleStatusChange}
            />
            <FormErrorMessage>
              {errorsStatus.status && errorsStatus.status.message}
            </FormErrorMessage>
          </FormControl>
          <Button
            type="submit"
            colorScheme="blue"
            isLoading={isSubmittingStatus}
            w="full"
          >
            Update Status
          </Button>
        </form>
      </Box>

      {/* Update Role, Designation & Department Form */}
      <Box
        p="6"
        borderWidth="1px"
        borderRadius="md"
        boxShadow="md"
        bg={useColorModeValue("gray.50", "gray.700")}
      >
        <Text fontSize="xl" fontWeight="semibold" mb="4" textAlign="center">
          Update Role, Designation & Department
        </Text>
        <form onSubmit={handleSubmitRole(onSubmitRole)}>
          <Grid templateColumns={{ base: "1fr", md: "repeat(2, 1fr)" }} gap="4">
            <GridItem>
              <FormControl isInvalid={!!errorsRole.role}>
                <FormLabel>Role</FormLabel>
                <AsyncSelect
                  variant="flushed"
                  name="role"
                  defaultOptions={rolesOptions}
                  options={rolesOptions}
                  isSearchable
                  placeholder="Select Role"
                  onChange={handleRoleChange}
                />
                <FormErrorMessage>
                  {errorsRole.role && errorsRole.role.message}
                </FormErrorMessage>
              </FormControl>
            </GridItem>
            <GridItem>
              <FormControl isInvalid={!!errorsRole.department}>
                <FormLabel>Department</FormLabel>
                <AsyncSelect
                  variant="flushed"
                  name="department"
                  defaultOptions={departmentOptions}
                  options={departmentOptions}
                  isSearchable
                  placeholder="Select Department"
                  onChange={handleDepartmentChange}
                />
                <FormErrorMessage>
                  {errorsRole.department && errorsRole.department.message}
                </FormErrorMessage>
              </FormControl>
            </GridItem>
            <GridItem colSpan={{ base: 1, md: 2 }}>
              <FormControl isInvalid={!!errorsRole.designation}>
                <FormLabel>Designation</FormLabel>
                <Input
                  variant="filled"
                  placeholder="Designation"
                  {...registerRole("designation", {
                    required: "Designation is required",
                  })}
                />
                <FormErrorMessage>
                  {errorsRole.designation && errorsRole.designation.message}
                </FormErrorMessage>
              </FormControl>
            </GridItem>
          </Grid>
          <Button
            mt="4"
            type="submit"
            colorScheme="blue"
            isLoading={isSubmittingRole}
            w="full"
          >
            Update Role & Department
          </Button>
        </form>
      </Box>

      {/* Assign Store Form */}
      <Box
        as="form"
        onSubmit={handleSubmitStore(onSubmitStore)}
        width="100%"
        p={6}
        bg={bgCard}
        borderRadius="lg"
        boxShadow="sm"
        mt={8}
      >
        <Text fontSize="xl" fontWeight="bold" mb={4} color={textColor}>
          Assign Store
        </Text>

        <Grid templateColumns="repeat(12, 1fr)" gap={6}>
          <GridItem colSpan={{ base: 12, md: 8 }}>
            <FormControl isInvalid={!!errorsStore.storeId}>
              <FormLabel>Select Store</FormLabel>
              <AsyncSelect
                isLoading={loadingStores}
                defaultOptions={storeOptions}
                loadOptions={loadStores}
                isSearchable
                onChange={handleStoreChange}
                placeholder="Select store"
                defaultValue={
                  employee.employee?.store
                    ? {
                        label: employee.employee.store.name,
                        value: employee.employee.store.id,
                      }
                    : undefined
                }
                isClearable
              />
              <FormErrorMessage>
                {errorsStore.storeId && errorsStore.storeId.message}
              </FormErrorMessage>
            </FormControl>
          </GridItem>

          <GridItem colSpan={{ base: 12, md: 4 }} alignSelf="end">
            <Button
              colorScheme="blue"
              isLoading={isSubmittingStore}
              type="submit"
              width="100%"
            >
              Assign Store
            </Button>
          </GridItem>
        </Grid>

        {error && (
          <Text color="red.500" mt={4}>
            {error}
          </Text>
        )}
      </Box>
    </Flex>
  );
};

export default EditEmployee;
