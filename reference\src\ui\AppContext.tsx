import * as React from "react";
import IAppState, { IAppContext } from "./interfaces/AppState";
import { IAction } from "./interfaces/Types";

const initialState: IAppState = {
  pendingFile: undefined,
  cart_inventory: undefined,
  oldCart: {
    items: [],
    totalAmount: 0,
    itemCount: 0,
  },
  cart: {
    items: [],
    totalAmount: 0,
    itemCount: 0,
  },
};

const AppContext = React.createContext<IAppContext>({
  state: {
    pendingFile: undefined,
    cart_inventory: undefined,
    oldCart: {
      items: [],
      totalAmount: 0,
      itemCount: 0,
    },
    cart: {
      items: [],
      totalAmount: 0,
      itemCount: 0,
    },
  },
  dispatch: () => {},
});

/**
 * Reducer function should simply digest the action payload and return a new state object
 */
const appReducer = (state: IAppState, action: IAction): typeof initialState => {
  const pendingFile = action.pendingFile;
  const cart_inventory = action.cart_inventory;
  const cartItem = action.cartItem;
  const cart = action.cart;

  switch (action.type) {
    case "ADD_ITEM_TO_CART": {
      if (cartItem) {
        const existingItem = state.cart.items.find(
          (item) => item.id === cartItem.id
        );

        if (existingItem) {
          // Update quantity for existing item
          return {
            ...state,
            cart: {
              items: state.cart.items.map((item) =>
                item.id === cartItem.id
                  ? { ...item, quantity: item.quantity + cartItem.quantity }
                  : item
              ),
              totalAmount:
                state.cart.totalAmount + cartItem.price * cartItem.quantity,
              itemCount: state.cart.itemCount, // Item count stays the same since no new item is added
            },
          };
        }
        // Add new item to cart
        return {
          ...state,
          cart: {
            items: [...state.cart.items, cartItem],
            totalAmount:
              state.cart.totalAmount + cartItem.price * cartItem.quantity,
            itemCount: state.cart.itemCount + 1,
          },
        };
      }
      return state;
    }

    case "UPDATE_SAVED_CART_ITEMS": {
      if (cart && cart_inventory) {
        // receive cart with items and save it to state also save the inventory id to state.cart_inventory
        return {
          ...state,
          cart: cart,
          oldCart: cart,
          cart_inventory: cart_inventory,
        };
      }
      return state;
    }

    case "REMOVE_ITEM_FROM_CART": {
      if (cartItem)
        return {
          ...state,
          cart: {
            items: state.cart.items.filter((item) => item.id !== cartItem.id),
            totalAmount:
              state.cart.totalAmount - cartItem.price * cartItem.quantity,
            itemCount: state.cart.itemCount - 1,
          },
        };
      return state;
    }

    case "UPDATE_CART_ITEM_QUANTITY": {
      if (cartItem) {
        let oldQuantity: number = 0;
        return {
          ...state,
          cart: {
            items: state.cart.items.map((item) => {
              if (item.id === cartItem.id) oldQuantity = item.quantity;
              return item.id === cartItem.id
                ? { ...item, quantity: cartItem.quantity }
                : item;
            }),
            totalAmount:
              state.cart.totalAmount +
              cartItem.price * cartItem.quantity -
              cartItem.price * oldQuantity,
            itemCount: state.cart.itemCount,
          },
        };
      }
      return state;
    }

    case "UPDATE_CART_ITEM_HOLD": {
      if (cartItem) {
        return {
          ...state,
          cart: {
            items: state.cart.items.map((item) =>
              item.id === cartItem.id ? { ...item, hold: cartItem.hold } : item
            ),
            totalAmount: state.cart.totalAmount,
            itemCount: state.cart.itemCount,
          },
        };
      }
      return state;
    }

    case "CLEAR_CART": {
      return {
        ...state,
        cart_inventory: undefined,
        oldCart: {
          items: [],
          totalAmount: 0,
          itemCount: 0,
        },
        cart: {
          items: [],
          totalAmount: 0,
          itemCount: 0,
        },
      };
    }

    case "SET_UPLOADING_FILE_URL": {
      if (pendingFile)
        return {
          ...state,
          pendingFile: pendingFile,
        };
      return state;
    }

    case "CLEAR_UPLOADING_FILE_URL": {
      return {
        ...state,
        pendingFile: undefined,
      };
    }

    default:
      throw new Error();
  }
};

const AppContextProvider: React.FunctionComponent<React.PropsWithChildren> = ({
  children,
}) => {
  const [state, dispatch] = React.useReducer(
    appReducer,
    initialState as IAppState
  );
  const value = { state, dispatch };
  return <AppContext.Provider value={value}>{children}</AppContext.Provider>;
};

export { AppContext, AppContextProvider };
