import { ComponentStyleConfig } from "@chakra-ui/react";

// Define MainPanel component styles
const MainPanel: ComponentStyleConfig = {
  baseStyle: {
    float: "right",
    maxWidth: "100%",
    overflow: "auto",
    position: "relative",
    maxHeight: "100%",
    transition: "all 0.33s cubic-bezier(0.685, 0.0473, 0.346, 1)",
    transitionDuration: ".2s, .2s, .35s",
    transitionProperty: "top, bottom, width",
    transitionTimingFunction: "linear, linear, ease",
  },
  variants: {
    main: () => ({
      float: "right", // For the "main" variant, position on the right
    }),
    rtl: () => ({
      float: "left", // For the "rtl" variant, position on the left (Right-to-Left layout)
    }),
  },
  defaultProps: {
    variant: "main", // Default variant is "main"
  },
};

// Export MainPanel component as part of the Chakra UI theme
export const MainPanelComponent = {
  components: {
    MainPanel,
  },
};
