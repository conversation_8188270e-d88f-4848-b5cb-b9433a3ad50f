{"expo": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "slug": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "version": "1.0.0", "extra": {"https": true, "router": {"origin": false}, "eas": {"projectId": "6849c42a-205d-4bd2-8f17-3e482a17fc0e"}}, "orientation": "portrait", "icon": "./assets/images/favicon.png", "scheme": "myapp", "userInterfaceStyle": "automatic", "splash": {"image": "./assets/images/logo.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "newArchEnabled": true}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/favicon.png", "backgroundColor": "#ffffff"}, "newArchEnabled": true, "package": "com.samxtu.TalisiaMob"}, "plugins": ["expo-router", "expo-camera"], "packagerOpts": {"sourceExts": ["js", "jsx", "ts", "tsx"], "config": "metro.config.js"}}}