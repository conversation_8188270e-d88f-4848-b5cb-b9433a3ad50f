/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useContext } from "react";
import {
  Box,
  Flex,
  VStack,
  useColorModeValue,
  Text,
  Avatar,
  Divider,
  Link,
  Icon,
} from "@chakra-ui/react";
import { NavLink, useHistory } from "react-router-dom";
import { MeContext } from "../Wrapper";

interface SidebarProps {
  routes: Array<{
    name: string;
    layout: string;
    path: string;
    appear: boolean;
    icon?: any;
  }>;
  navbarHeight: string | number; // Pass the height of the navbar
  footerHeight: string | number; // Pass the height of the navbar
}

const Sidebar: React.FC<SidebarProps> = ({
  routes,
  navbarHeight,
  footerHeight,
}) => {
  // Dynamic styles for light and dark modes
  const bgColor = useColorModeValue("gray.100", "gray.800");
  const textColor = useColorModeValue("gray.700", "gray.200");
  const hoverBgColor = useColorModeValue("teal.100", "teal.600");
  const hoverTextColor = useColorModeValue("teal.900", "white");
  const borderColor = useColorModeValue("gray.300", "gray.700");

  const me = useContext(MeContext);
  const history = useHistory();

  return (
    <Box
      bg={bgColor}
      w="250px"
      h={`calc(100vh - ${Number(navbarHeight) + Number(footerHeight)}px)`} // Adjust height to account for navbar
      mt={`${navbarHeight}px`} // Push sidebar content below the navbar
      mb={`${footerHeight}px`} // Push sidebar content below the navbar
      p={4}
      boxShadow="lg"
      position="fixed"
      left="0"
      top="0"
      zIndex="2000" // Ensure sidebar overlays other elements
      borderRight="1px solid"
      borderColor={borderColor}
    >
      {/* User Profile Section */}
      <Flex
        align="center"
        mb={8}
        p={3}
        borderRadius="md"
        bg={useColorModeValue("white", "gray.700")}
        boxShadow="md"
        onClick={() =>
          history.push({ pathname: "user", state: { employee: me } })
        }
      >
        <Avatar
          size="lg"
          name={me?.firstname + " " + me?.lastname}
          src={me?.image ?? ""}
        />
        <VStack align="start" spacing={0} ml={4}>
          <Text fontWeight="bold" fontSize="lg" color={textColor}>
            {me?.firstname + " " + me?.lastname}
          </Text>
          <Text fontSize="sm" color={useColorModeValue("gray.500", "gray.400")}>
            {me?.role.name}
          </Text>
        </VStack>
      </Flex>

      <Divider borderColor={borderColor} mb={4} />

      {/* Navigation Menu */}
      <VStack align="stretch" spacing={2}>
        {routes
          .filter((temproute) => temproute.appear === true)
          .map((route, index) => (
            <NavLink
              key={index}
              to={route.layout + route.path}
              style={{ textDecoration: "none" }}
            >
              <Link
                _hover={{
                  textDecoration: "none",
                  bg: hoverBgColor,
                  color: hoverTextColor,
                }}
                px={3}
                py={2}
                borderRadius="md"
                color={textColor}
                display="flex"
                alignItems="center"
                transition="background-color 0.2s, color 0.2s"
              >
                {route.icon && <Icon as={route.icon} mr={3} />}
                {route.name}
              </Link>
            </NavLink>
          ))}
      </VStack>
    </Box>
  );
};

export default Sidebar;
