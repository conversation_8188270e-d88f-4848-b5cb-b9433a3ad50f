/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useState, useEffect } from "react";
import { Route, Redirect, RouteProps } from "react-router-dom";
import { useMeQuery } from "../generated/graphql";
import { Wrapper } from "../components/Wrapper";
import { Center, Spinner } from "@chakra-ui/react";

// Memoization cache for companyId
let cachedCompanyId: { companyId: string | null; error?: string } | null = null;

const getMemoizedCompanyId = async (): Promise<{
  companyId: string | null;
  error?: string;
}> => {
  if (cachedCompanyId !== null) {
    return cachedCompanyId; // Return cached result
  }

  try {
    // Corrected IPC call based on your preload setup
    const result = await window.electron.invoke("get-company-id", {});
    cachedCompanyId = result; // Cache the result
    return result;
  } catch (error) {
    console.error("IPC error loading company ID:", error);
    cachedCompanyId = { companyId: null, error: String(error) };
    return cachedCompanyId;
  }
};

// Define props interface
interface AuthRouteProps extends RouteProps {
  component: React.ComponentType<any>; // Replace 'any' with specific props if known
}

const AuthRoute: React.FC<AuthRouteProps> = ({
  component: Component,
  ...rest
}) => {
  const [{ data, fetching }] = useMeQuery({
    requestPolicy: "network-only",
  });
  const [loginMode, setLoginMode] = useState<"login" | "sign-in">("login");
  const [isChecked, setIsChecked] = useState(false);

  // Check company ID once on mount using memoized function
  useEffect(() => {
    const checkCompanyId = async () => {
      try {
        const result = await getMemoizedCompanyId();
        if (result.companyId) {
          setLoginMode("sign-in");
        } else {
          setLoginMode("login");
          if (result.error) {
            console.warn("Failed to load company ID:", result.error);
          }
        }
      } catch (error) {
        // Shouldn't reach here due to try-catch in getMemoizedCompanyId, but included for safety
        console.error("Unexpected error loading company ID:", error);
        setLoginMode("login");
      } finally {
        setIsChecked(true);
      }
    };
    checkCompanyId();
  }, []);

  // Show spinner while checking or fetching
  if (!isChecked || fetching) {
    return (
      <Center height="100vh">
        <Spinner
          thickness="4px"
          speed="0.65s"
          emptyColor="gray.200"
          color="blue.500"
          size="xl"
        />
      </Center>
    );
  }

  const redirectPath = data?.me?.id
    ? undefined
    : {
        pathname: `/${loginMode}`,
        search:
          rest.location?.pathname && rest.location.pathname !== "/"
            ? `?next=${encodeURIComponent(rest.location.pathname)}`
            : undefined,
      };

  return (
    <Route
      {...rest}
      render={(props) =>
        data?.me?.id ? (
          <Wrapper me={data.me}>
            <Component {...props} />
          </Wrapper>
        ) : (
          <Redirect to={redirectPath || `/${loginMode}`} />
        )
      }
    />
  );
};

export default AuthRoute;

// Optional: Export a function to invalidate the cache if needed
// eslint-disable-next-line react-refresh/only-export-components
export const invalidateCompanyIdCache = () => {
  cachedCompanyId = null;
};
