query getPatients {
  getPatients {
    id
    firstname
    lastname
    middlename
    phone
    email
    status
    dateOfBirth
    nationalId
    address {
      country
      city
      street
      ward
      district
    }
    religion
    gender
    fileNumber
    insuranceId
    insuranceUserId
    insuranceStatus
    insuranceProvider
    insuranceSchemeId
    insuranceCardNumber
    nextOfKinName
    nextOfKinPhone
    nextOfKinRelationship
  }
}

query getPatient($id: Float!) {
  getPatient(id: $id) {
    id
    firstname
    lastname
    middlename
    phone
    email
    status
    dateOfBirth
    nationalId
    address {
      country
      city
      street
      ward
      district
    }
    religion
    gender
    fileNumber
    insuranceId
    insuranceUserId
    insuranceStatus
    insuranceProvider
    insuranceSchemeId
    insuranceCardNumber
    nextOfKinName
    nextOfKinPhone
    nextOfKinRelationship
  }
}

query getVisits {
  getVisits {
    id
    status
    type
    reason
    consultation
    currentLocation
    clientId
    client {
      id
      firstname
      middlename
      lastname
      status
      email
      phone
      insuranceProvider
      insuranceId
      insuranceUserId
      insuranceStatus
      insuranceSchemeId
      insuranceCardNumber
      gender
      dateOfBirth
      fileNumber
      nationalId
      bloodGroup
      registererId
    }
    bills {
      id
      cleared
      amount
      paymentType
    }
    visitToClinics {
      id
      clinicId
    }
  }
}
