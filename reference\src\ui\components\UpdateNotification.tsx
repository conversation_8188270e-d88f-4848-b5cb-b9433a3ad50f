import React, { useEffect, useState } from "react";
import {
  <PERSON><PERSON>,
  AlertIcon,
  Button,
  Progress,
  VStack,
  Text,
  useToast,
} from "@chakra-ui/react";

interface UpdateInfo {
  version: string;
  releaseDate: string;
}

const UpdateNotification: React.FC = () => {
  const [updateAvailable, setUpdateAvailable] = useState(false);
  const [downloadProgress, setDownloadProgress] = useState(0);
  const [updateInfo, setUpdateInfo] = useState<UpdateInfo | null>(null);
  const [isDownloaded, setIsDownloaded] = useState(false);
  const toast = useToast();

  useEffect(() => {
    // Listen for update events from main process
    window.electron.on("update-available", (info: UpdateInfo) => {
      setUpdateAvailable(true);
      setUpdateInfo(info);
      toast({
        title: "Update Available",
        description: `Version ${info.version} is available to download`,
        status: "info",
        duration: null,
        isClosable: true,
      });
    });

    window.electron.on(
      "download-progress",
      (progressObj: { percent: number }) => {
        setDownloadProgress(progressObj.percent);
      }
    );

    window.electron.on("update-downloaded", () => {
      setIsDownloaded(true);
      toast({
        title: "Update Ready",
        description: "Update has been downloaded and is ready to install",
        status: "success",
        duration: null,
        isClosable: true,
      });
    });

    window.electron.on("update-error", (error: string) => {
      toast({
        title: "Update Error",
        description: error,
        status: "error",
        duration: null,
        isClosable: true,
      });
    });

    // Clean up listeners
    return () => {
      window.electron.on("update-available", () => {});
      window.electron.on("download-progress", () => {});
      window.electron.on("update-downloaded", () => {});
      window.electron.on("update-error", () => {});
    };
  }, [toast]);

  const handleInstallUpdate = async () => {
    try {
      await window.electron.invoke("start-update", {});
    } catch (error) {
      toast({
        title: "Installation Error",
        description: String(error),
        status: "error",
        duration: 5000,
        isClosable: true,
      });
    }
  };

  if (!updateAvailable) return null;

  return (
    <Alert
      status="info"
      variant="subtle"
      flexDirection="column"
      alignItems="center"
      justifyContent="center"
      textAlign="center"
      height="200px"
      position="fixed"
      bottom="4"
      right="4"
      width="300px"
      borderRadius="md"
      boxShadow="lg"
    >
      <AlertIcon boxSize="40px" mr={0} />
      <VStack spacing={3} mt={4}>
        <Text>
          New version {updateInfo?.version} is{" "}
          {isDownloaded ? "ready to install" : "available"}
        </Text>
        {!isDownloaded && downloadProgress > 0 && (
          <Progress
            value={downloadProgress}
            size="sm"
            width="100%"
            colorScheme="blue"
          />
        )}
        {isDownloaded && (
          <Button colorScheme="blue" onClick={handleInstallUpdate}>
            Install and Restart
          </Button>
        )}
      </VStack>
    </Alert>
  );
};

export default UpdateNotification;
