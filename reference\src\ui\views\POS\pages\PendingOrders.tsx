import React, { useState, useEffect, useContext } from "react";
import {
  Flex,
  Button,
  Text,
  useToast,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Badge,
  useDisclosure,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  Box,
  Input,
} from "@chakra-ui/react";
import { MdOutlineMoveUp } from "react-icons/md";
import {
  Item,
  Transfer,
  useGetOpenTabsQuery,
  useServePendingOrderMutation,
} from "../../../generated/graphql";
import { formatToMoney } from "../../../utils/Helpers";
import { hasPermission } from "../../../interfaces/Helpers";
import { MeContext } from "../../../components/Wrapper";

const PendingOrders: React.FC = () => {
  const toast = useToast({ position: "top" });
  const me = useContext(MeContext);

  const [{ data: pendingOrders }, refetchPendingOrders] = useGetOpenTabsQuery({
    requestPolicy: "network-only",
  });
  const [, serveTransfer] = useServePendingOrderMutation();

  const { isOpen, onOpen, onClose } = useDisclosure();
  const [selectedTransfer, setSelectedTransfer] = useState<Transfer | null>(
    null
  );
  const [selectedItem, setSelectedItem] = useState<Item | null>(null);
  const [searchTerm, setSearchTerm] = useState("");

  const handleServe = async () => {
    if (selectedTransfer) {
      const { data } = await serveTransfer({ transferId: selectedTransfer.id });
      if (data?.servePendingOrder.error) {
        toast({
          title: data.servePendingOrder.error.message,
          status: "error",
          isClosable: true,
        });
      } else {
        refetchPendingOrders();
        onClose();
        toast({
          title: "Transfer served successfully!",
          variant: "left-accent",
          status: "success",
          isClosable: true,
        });
      }
    }
  };

  useEffect(() => {
    const interval = setInterval(() => {
      refetchPendingOrders({ requestPolicy: "network-only" });
    }, 5000);
    return () => clearInterval(interval);
  }, [refetchPendingOrders]);

  const filteredOrders = pendingOrders?.getOpenTabs
    .filter((sale) => {
      if (
        hasPermission(me?.permissions, [
          "Pending Orders>All",
          "Pending Orders>Edit",
          "Pending Orders>No Confirm Dialog",
          "Pending Orders>Delete",
        ])
      )
        return true;
      else return sale.consumer?.id === me?.id || sale.keeper?.id === me?.id;
    })
    .filter((order) => {
      const orderHasPendingItem = order.transfers.some(
        (transfer) => !transfer.dispatched
      );

      if (!orderHasPendingItem) return false;

      const matchesItem = order.items.some((item) =>
        item.name.toLowerCase().includes(searchTerm.toLowerCase())
      );
      const matchesCustomerTag = order.customerTag
        ?.toLowerCase()
        .includes(searchTerm.toLowerCase());
      const matchesAttendingEmployee =
        `${order.consumer?.user.firstname} ${order.consumer?.user.lastname}`
          .toLowerCase()
          .includes(searchTerm.toLowerCase());

      return matchesItem || matchesCustomerTag || matchesAttendingEmployee;
    });

  return (
    <Flex
      direction="column"
      position="relative"
      mb="40px"
      mt="50px"
      flex="1" // Take remaining vertical space
      overflowY="auto"
      maxHeight={{ base: "calc(100vh - 0px)", md: "calc(100vh - 0px)" }}
      sx={{
        "&::-webkit-scrollbar": {
          width: "1px",
        },
        "&::-webkit-scrollbar-thumb": {
          background: "gray.300",
          borderRadius: "4px",
        },
        "&::-webkit-scrollbar-thumb:hover": {
          background: "gray.500",
        },
      }}
    >
      <Flex
        w="100%"
        mx="auto"
        justifyContent="space-between"
        mb="20px"
        pt={{ md: "0px" }}
        direction="column"
      >
        <Flex direction="column" overflow="auto">
          {/* Title with enhanced styling */}
          <Text
            fontSize="3xl"
            fontWeight="extrabold"
            textAlign="center"
            mb="12px"
            bgGradient="linear(to-r, yellow.400, red.500)"
            bgClip="text"
            textShadow="1px 1px 3px rgba(0, 0, 0, 0.2)"
            p={2}
          >
            Pending Orders
          </Text>

          {/* Search Input */}
          <Input
            placeholder="Search by item name, customer tag, or attending employee"
            size="md"
            mb="16px"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />

          <Table variant="simple">
            <Thead>
              <Tr>
                <Th>Order No</Th>
                <Th>Item Name</Th>
                <Th>Quantity</Th>
                <Th>Sale Amount</Th>
                <Th>Status</Th>
                <Th>Action</Th>
              </Tr>
            </Thead>
            <Tbody>
              {filteredOrders?.map((inventory) => (
                <React.Fragment key={inventory.id}>
                  <Tr>
                    <Td colSpan={2} fontWeight="bold">
                      <Text fontSize="lg">Order No #{inventory.id}</Text>
                    </Td>
                    <Td colSpan={2} fontWeight="bold">
                      {inventory.customerTag &&
                        `Customer Tag: ${inventory.customerTag}`}
                    </Td>
                    <Td colSpan={2} fontWeight="bold">
                      {inventory.customerTag &&
                        inventory.consumer &&
                        `Attending: ${
                          inventory.consumer?.user.firstname +
                          " " +
                          inventory.consumer?.user.lastname
                        }`}
                    </Td>
                  </Tr>
                  {inventory.transfers
                    .filter((trans) => trans.dispatched === false)
                    .map((transfer) => {
                      const item = inventory.items.find(
                        (item) => item.id === transfer.itemId
                      );

                      return (
                        <Tr key={transfer.id}>
                          <Td>{inventory.id}</Td>
                          <Td>{item!.name}</Td>
                          <Td>
                            {transfer.quantity} {item!.unit}
                          </Td>
                          <Td>
                            {formatToMoney(transfer.price * transfer.quantity)}
                          </Td>
                          <Td>
                            <Badge
                              colorScheme="yellow"
                              variant="solid"
                              color="black"
                              borderRadius="full"
                              px={3}
                              py={1}
                            >
                              Pending
                            </Badge>
                          </Td>
                          <Td>
                            {hasPermission(me?.permissions, [
                              "Pending Orders>Edit",
                              "Pending Orders>No Confirm Dialog",
                              "Pending Orders>Delete",
                            ]) && (
                              <Button
                                size="sm"
                                colorScheme="green"
                                leftIcon={<MdOutlineMoveUp />}
                                onClick={() => {
                                  setSelectedTransfer(transfer as Transfer);
                                  setSelectedItem(item as Item);
                                  if (
                                    hasPermission(me?.permissions, [
                                      "Pending Orders>No Confirm Dialog",
                                      "Pending Orders>Delete",
                                    ])
                                  )
                                    return handleServe();
                                  else onOpen();
                                }}
                                _hover={{
                                  bg: "green.600",
                                  transform: "scale(1.05)",
                                }}
                                transition="all 0.3s"
                              >
                                Serve
                              </Button>
                            )}
                          </Td>
                        </Tr>
                      );
                    })}
                </React.Fragment>
              ))}
            </Tbody>
          </Table>
        </Flex>
      </Flex>

      {/* Confirmation Modal */}
      {selectedTransfer && selectedItem && (
        <Modal isOpen={isOpen} onClose={onClose} size="lg">
          <ModalOverlay />
          <ModalContent bg="gray.50" borderRadius="10px" boxShadow="lg">
            <ModalHeader
              fontSize="3xl"
              color="teal.600"
              textAlign="center"
              fontWeight="bold"
            >
              Serve Pending Order Item
            </ModalHeader>
            <ModalBody>
              <Box
                bg="white"
                p={6}
                borderRadius="8px"
                boxShadow="md"
                borderWidth="1px"
                borderColor="gray.200"
                mb={6}
              >
                <Table variant="simple" size="md">
                  <Thead>
                    <Tr>
                      <Th fontSize="lg" color="gray.600" fontWeight="bold">
                        Item Details
                      </Th>
                      <Th
                        isNumeric
                        fontSize="lg"
                        color="gray.600"
                        fontWeight="bold"
                      >
                        Value
                      </Th>
                    </Tr>
                  </Thead>
                  <Tbody>
                    <Tr>
                      <Td fontSize="lg" fontWeight="bold" color="teal.600">
                        Item Name
                      </Td>
                      <Td isNumeric fontSize="lg">
                        {selectedItem?.name}
                      </Td>
                    </Tr>
                    <Tr>
                      <Td fontSize="lg" fontWeight="bold" color="teal.600">
                        Price
                      </Td>
                      <Td isNumeric fontSize="lg">{`${formatToMoney(
                        selectedTransfer!.price!
                      )}`}</Td>
                    </Tr>
                    <Tr>
                      <Td fontSize="lg" fontWeight="bold" color="teal.600">
                        Quantity
                      </Td>
                      <Td isNumeric fontSize="lg">
                        {selectedTransfer?.quantity}
                      </Td>
                    </Tr>
                    <Tr>
                      <Td fontSize="lg" fontWeight="bold" color="teal.600">
                        Total
                      </Td>
                      <Td isNumeric fontSize="lg">
                        {`${formatToMoney(
                          selectedTransfer!.price * selectedTransfer!.quantity
                        )}`}
                      </Td>
                    </Tr>
                  </Tbody>
                </Table>
              </Box>
            </ModalBody>
            <ModalFooter>
              <Button
                colorScheme="blue"
                mr={3}
                onClick={onClose}
                _hover={{ bg: "blue.600" }}
                fontWeight="bold"
                fontSize="lg"
                px={8}
              >
                Close
              </Button>
              <Button
                colorScheme="green"
                onClick={handleServe}
                _hover={{ bg: "green.600" }}
                fontWeight="bold"
                fontSize="lg"
                px={8}
              >
                Confirm Serve
              </Button>
            </ModalFooter>
          </ModalContent>
        </Modal>
      )}
    </Flex>
  );
};

export default PendingOrders;
