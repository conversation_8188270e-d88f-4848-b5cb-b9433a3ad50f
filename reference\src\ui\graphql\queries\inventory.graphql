# get all items
query getAllItems {
  getAllItems {
    id
    name
    type
    barcode
    description
    image
    sellingPrice
    reorder
    reference
    internal
    unit
    stock
    units {
      id
      name
      quantity
      price
    }
  }
}

# get all services
query getAllServices {
  getAllServices {
    id
    name
    description
    sellingPrice
    reference
  }
}

# get all items
query getStoreItems($storeId: Float!) {
  getStoreItems(storeId: $storeId) {
    id
    name
    type
    image
    barcode
    description
    sellingPrice
    reorder
    reference
    internal
    unit
    stock
    units {
      id
      name
      quantity
      price
    }
  }
}

# get internal items
query getInternalItems {
  getInternalItems {
    id
    name
    type
    image
    barcode
    description
    reorder
    reference
    internal
    unit
    stock
    units {
      id
      name
      quantity
      price
    }
  }
}

# get merchandise items
query getMerchandiseItems {
  getMerchandiseItems {
    id
    name
    type
    barcode
    image
    description
    sellingPrice
    reorder
    reference
    internal
    unit
    stock
    units {
      id
      name
      quantity
      price
    }
  }
}

# get one item
query getItem($id: Float!) {
  getItem(id: $id) {
    id
    name
    type
    barcode
    image
    description
    internal
    reorder
    reference
    unit
    stock
    sellingPrice
    units {
      id
      name
      quantity
      price
    }
    imports {
      id
      importDate
      supplier
      quantity
      importPrice
      sellingPrice
    }
    inventoryTransfers {
      id
      type
      details
      transferDate
      sourceStoreId
      destinationStoreId
    }
    transfers {
      id
      inventoryId
      itemId
      quantity
    }
  }
}

# get sales
query getSales($date: DateTime) {
  getSales(date: $date) {
    id
    createdAt
    details
    type
    granted
    dispatched
    sourceStoreId
    keeper {
      id
      userId
    }
    approver {
      id
      userId
    }
    bill {
      amount
      cleared
      paymentType
    }
    transfers {
      id
      itemId
      quantity
      details
    }
    items {
      id
      name
      unit
      sellingPrice
      type
      reference
      description
    }
  }
}

query getSalesPOS {
  getSalesPOS {
    id
    createdAt
    details
    type
    granted
    dispatched
    sourceStoreId
    customerTag
    sourceStore {
      id
      name
    }
    consumer {
      id
      userId
      user {
        id
        firstname
        lastname
      }
    }
    keeper {
      id
      userId
      user {
        id
        firstname
        lastname
      }
    }
    bill {
      amount
      cleared
      paymentType
    }
    transfers {
      id
      itemId
      quantity
      details
      price
      dispatched
    }
    items {
      id
      name
      unit
      sellingPrice
      type
      reference
      description
    }
  }
}

# get open tabs (sales with uncleared bills)
query getOpenTabs {
  getOpenTabs {
    id
    createdAt
    details
    type
    granted
    dispatched
    sourceStoreId
    customerTag
    sourceStore {
      id
      name
    }
    consumer {
      id
      userId
      user {
        id
        firstname
        lastname
      }
    }
    keeper {
      id
      userId
      user {
        id
        firstname
        lastname
      }
    }
    bill {
      createdAt
      amount
      cleared
      paymentType
    }
    transfers {
      id
      itemId
      inventoryId
      quantity
      details
      price
      dispatched
    }
    items {
      id
      name
      unit
      sellingPrice
      type
      reference
      description
      stock
    }
  }
}

# get all company stores
query getStores {
  getStores {
    id
    name
    primary
    address
    storeKeepers {
      id
    }
  }
}

# get all inventory actions/transfers
query getInventoryTransfers($type: String) {
  getInventoryTransfers(type: $type) {
    id
    details
    type
    granted
    received
    transferDate
    sourceStore {
      id
      name
    }
    destinationStore {
      id
      name
    }
  }
}

#get one inventory action/transfer
query getInventoryTransfer($id: Float!) {
  getInventoryTransfer(id: $id) {
    id
    details
    type
    granted
    received
    transferDate
    sourceStore {
      id
      name
    }
    destinationStore {
      id
      name
    }
    keeper {
      id
      userId
    }
    consumer {
      id
      userId
    }
    transfers {
      id
      itemId
      quantity
    }
    items {
      id
      name
      unit
      sellingPrice
      type
    }
  }
}

# get items transfers
query getItemTransfers($itemId: Float!, $type: String) {
  getItemTransfers(itemId: $itemId, type: $type) {
    id
    details
    quantity
    batch
    inventoryTransfer {
      id
      updatedAt
      details
      type
      transferDate
      sourceStoreId
      destinationStoreId
    }
    item {
      id
      name
      unit
    }
  }
}

#  query write offs
query GetWriteOffsByCompany {
  getWriteOffsByCompany {
    id
    createdAt
    quantity
    details
    item {
      id
      name
      unit
    }
  }
}

# query dispatches
query getDispatches {
  getDispatches {
    id
    updatedAt
    transferDate
    granted
    dispatched
    received
    sourceStore {
      id
      name
    }
    destinationStore {
      id
      name
    }
    items {
      id
      name
      unit
    }
    keeperId
    consumerId
    transfers {
      id
      itemId
      quantity
      batch
    }
  }
}

# query transfers
query getTransfers {
  getTransfers {
    id
    updatedAt
    transferDate
    granted
    dispatched
    received
    sourceStore {
      id
      name
    }
    destinationStore {
      id
      name
    }
    items {
      id
      name
      unit
    }
    keeperId
    consumerId
    transfers {
      id
      itemId
      quantity
      batch
    }
  }
}

#get batches for one item
query getItemBatchStocks($itemId: Float!) {
  getItemBatchStocks(itemId: $itemId) {
    id
    batch
    expireDate
    stock
    storeItemStocks {
      id
      storeId
      stock
    }
  }
}

# get batches for a store with stock
query getBatchStockForStore($itemId: Float, $storeId: Float) {
  getBatchStockForStore(itemId: $itemId, storeId: $storeId) {
    itemId
    batch
    expireDate
    stock
    storeItemStocks {
      storeId
      stock
    }
  }
}

#get stores stocks for one item without batches (POS no batch tracking)
query getItemStoreStocksPOS($itemId: Float!) {
  getItemStoreStocks(itemId: $itemId) {
    id
    storeId
    stock
    store {
      id
      name
    }
  }
}

query getItemStoreStocks($itemId: Float!) {
  getItemStoreStocks(itemId: $itemId) {
    id
    storeId
    stock
    batchId
    store {
      id
      name
    }
  }
}

query getItemBatchImports($itemId: Float!) {
  getItemBatchImports(itemId: $itemId) {
    id
    importPrice
    batch
  }
}
