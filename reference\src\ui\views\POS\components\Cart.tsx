import React, { useContext, useState } from "react";
import {
  Box,
  Text,
  VStack,
  Flex,
  IconButton,
  Button,
  Input,
  Checkbox,
  Divider,
  useColorModeValue,
  HStack,
  useToast,
} from "@chakra-ui/react";
import { DeleteIcon, AddIcon, MinusIcon } from "@chakra-ui/icons";
import { AppContext } from "../../../AppContext";
import { ActionType } from "../../../interfaces/Types";
import {
  useQuickSaleMutation,
  useServePayLaterMutation,
  User,
  useUpdateBillMutation,
} from "../../../generated/graphql";
import { useHistory } from "react-router";
import { MeContext } from "../../../components/Wrapper";
import { hasPermission } from "../../../interfaces/Helpers";

interface CartProps {
  selectedEmployee?: User; // Selected employee's name
  selectedTable: string; // Selected table name
}

const Cart: React.FC<CartProps> = ({ selectedEmployee, selectedTable }) => {
  const { state, dispatch } = useContext(AppContext);
  const me = useContext(MeContext);
  const [discount, setDiscount] = useState(0);

  const history = useHistory();

  const borderColor = useColorModeValue("gray.200", "gray.600");
  const bgColorBox = useColorModeValue("whiteAlpha.900", "gray.700");
  // mutations
  const [, quickSale] = useQuickSaleMutation();
  const [, servePayLater] = useServePayLaterMutation();
  const [, updateBill] = useUpdateBillMutation();

  // Chakra UI toast for notifications
  const toast = useToast({ position: "top" });

  const handleRemoveItem = (id: number) => {
    dispatch({
      type: ActionType.REMOVE_ITEM_FROM_CART,
      cartItem: {
        id,
        name: "",
        unit: "",
        price: 0,
        quantity: 0,
        stock: 0,
        hold: false,
      },
    });
  };

  const handleHoldItem = (id: number, checked: boolean) => {
    dispatch({
      type: ActionType.UPDATE_CART_ITEM_HOLD,
      cartItem: {
        id,
        name: "",
        unit: "",
        price: 0,
        quantity: 0,
        stock: 0,
        hold: checked,
      },
    });
  };
  const handleQuantityChange = (id: number, increment: boolean) => {
    const item = state.cart.items.find((item) => item.id === id);
    if (item) {
      let newQuantity = item.quantity;
      const oldItem = state.oldCart.items.find((ite) => ite.id === item.id);
      const oldQuantity = oldItem ? oldItem.quantity : 0;

      if (increment) {
        // Increment the quantity, but prevent it from exceeding stock
        if (item.quantity < item.stock + oldQuantity) {
          newQuantity = item.quantity + 1;
        } else {
          // Show toast if trying to increment beyond available stock
          toast({
            title: "Stock Unavailable",
            description: `You cannot add more than ${item.stock} items.`,
            status: "error",
            duration: 3000,
            isClosable: true,
          });
          return; // Prevent further execution if we're at stock limit
        }
      } else {
        // Decrement the quantity, but ensure it doesn't go below 1
        newQuantity = Math.max(1, item.quantity - 1);
      }

      // Update cart only if the quantity has actually changed
      if (newQuantity !== item.quantity) {
        dispatch({
          type: ActionType.UPDATE_CART_ITEM_QUANTITY,
          cartItem: { ...item, quantity: newQuantity },
        });
      }
    }
  };

  const handleClearCart = () => {
    if (state.cart_inventory) {
      dispatch({ type: ActionType.CLEAR_CART });
      history.push("open-tabs");
    } else dispatch({ type: ActionType.CLEAR_CART });
  };

  const handleBillCart = async () => {
    if (!state.cart_inventory && !selectedEmployee && !selectedTable)
      return toast({
        title: "Bill not saved",
        description: "Please select employee or table to bill.",
        status: "warning",
        duration: 3000,
        isClosable: true,
      });
    const itemsForSale = state.cart.items.map((item) => {
      return {
        itemId: item.id,
        quantity: item.quantity,
        unit: item.unit,
        hold: item.hold,
      };
    });

    try {
      // Attempt to submit the sale
      let sale;
      if (state.cart_inventory) {
        sale = await updateBill({
          inventoryId: state.cart_inventory,
          args: itemsForSale,
        });
        if (sale.data?.updateBill?.error) throw sale.data.updateBill.error;
      } else {
        sale = await servePayLater({
          args: itemsForSale,
          servedTo:
            selectedEmployee != null && selectedEmployee.employee != null
              ? selectedEmployee.employee.id
              : null,
          customerTag: selectedTable,
        });
        if (sale.data?.servePayLater?.error)
          throw sale.data.servePayLater.error;
      }
      if (sale.error) throw sale.error;

      // If the sale is successful, show a success toast
      if (state.cart_inventory) history.push("open-tabs");
      dispatch({ type: ActionType.CLEAR_CART });
      return toast({
        title: "Bill Created!",
        description: `Bill saved successful.`,
        status: "success",
        duration: 4000,
        isClosable: true,
      });
    } catch (error) {
      // Optionally, log the error for debugging
      console.error("Error during billing:", error);
      // In case of error, show an error toast
      toast({
        title: "Sale Failed",
        description: "There was an issue saving this bill. Please try again.",
        status: "error",
        duration: 5000,
        isClosable: true,
      });
    }
  };

  const handlePayedCart = async () => {
    const itemsForSale = state.cart.items.map((item) => {
      return {
        itemId: item.id,
        quantity: item.quantity,
        unit: item.unit,
      };
    });

    try {
      // Attempt to submit the sale
      const sale = await quickSale({ args: itemsForSale });
      if (sale.error) throw sale.error;
      else if (sale.data?.quickSale.error) throw sale.data.quickSale.error;
      // If the sale is successful, show a success toast
      dispatch({ type: ActionType.CLEAR_CART });
      return toast({
        title: "Sale Completed!",
        description: `Sale successful.`,
        status: "success",
        duration: 4000,
        isClosable: true,
      });
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
    } catch (error: any) {
      // In case of error, show an error toast
      toast({
        title: "Sale Failed",
        description:
          error.message ||
          "There was an issue processing your sale. Please try again.",
        status: "error",
        duration: 5000,
        isClosable: true,
      });
      // Optionally, log the error for debugging
      console.error("Error during quickSale:", error);
    }
  };

  const totalAmount = state.cart.items.reduce(
    (sum, item) => sum + item.price * item.quantity,
    0
  );

  return (
    <Box
      bg={bgColorBox}
      borderRadius="lg"
      borderWidth="1px"
      borderColor={borderColor}
      p={4}
      shadow="md"
      position="relative"
    >
      {/* Customer Order Header */}
      <Flex
        justify="space-between"
        flexDirection={"column"}
        align="center"
        mb={4}
      >
        <Box display="flex" justifyContent="center" width="100%">
          <Text fontWeight="bold" fontSize="lg" color="teal.600" margin="auto">
            {state.cart_inventory ? "Update Customer Tab" : "Customer Order"}
          </Text>
        </Box>

        {hasPermission(me?.permissions, [
          "POS>Hold Order",
          "POS>Employee Order",
        ]) &&
          !state.cart_inventory && (
            <Box display="flex" justifyContent="center" width="100%">
              <Text fontSize="sm" color="gray.500" textAlign="center">
                Employee:{" "}
                {selectedEmployee
                  ? selectedEmployee.firstname + " " + selectedEmployee.lastname
                  : "None"}{" "}
                | Customer: {selectedTable || "None"}
              </Text>
            </Box>
          )}
      </Flex>

      <Divider mb={4} />

      {/* Cart Items */}
      <VStack align="stretch" spacing={4}>
        {state.cart.items.length === 0 ? (
          <Text textAlign="center" color="gray.500">
            No items in the order
          </Text>
        ) : (
          state.cart.items.map((item) => (
            <Box
              key={item.id}
              p={3}
              borderRadius="md"
              borderWidth="1px"
              borderColor={borderColor}
              bg={bgColorBox}
            >
              <Flex justify="space-between" align="center" mb={2}>
                <Text fontWeight="bold">{item.name}</Text>
                <Text color="teal.500">{item.price * item.quantity} Tsh</Text>
              </Flex>
              <Flex justify="space-between" align="center">
                <HStack>
                  <Checkbox
                    size="lg"
                    colorScheme="teal"
                    onChange={(e) => handleHoldItem(item.id, e.target.checked)}
                  >
                    Hold
                  </Checkbox>
                </HStack>
                <HStack spacing={2}>
                  <IconButton
                    aria-label="Decrease quantity"
                    icon={<MinusIcon />}
                    size="sm"
                    onClick={() => handleQuantityChange(item.id, false)}
                  />
                  <Text>{item.quantity}</Text>
                  <IconButton
                    aria-label="Increase quantity"
                    icon={<AddIcon />}
                    size="sm"
                    onClick={() => handleQuantityChange(item.id, true)}
                  />
                  <IconButton
                    aria-label="Remove item"
                    icon={<DeleteIcon />}
                    size="sm"
                    colorScheme="red"
                    onClick={() => handleRemoveItem(item.id)}
                  />
                </HStack>
              </Flex>
            </Box>
          ))
        )}
      </VStack>

      {/* Discount Input */}
      <Flex align="center" justify="flex-end" mt={6} gap={2}>
        <Text fontWeight="medium">Discount:</Text>
        <Input
          type="number"
          value={discount}
          onChange={(e) => setDiscount(Number(e.target.value))}
          size="md"
          width="100px"
          textAlign="right"
        />
        <Text>Tsh</Text>
      </Flex>

      {/* Total Amount */}
      <Flex justify="space-between" align="center" mt={4} fontWeight="bold">
        <Text>Total:</Text>
        <Text fontSize="lg" color="teal.600">
          {totalAmount - discount} Tsh
        </Text>
      </Flex>

      {/* Action Buttons */}
      <Flex justify="space-between" mt={6}>
        {state.cart_inventory ? (
          <>
            <Button
              size="sm"
              colorScheme="gray"
              onClick={handleClearCart}
              borderRadius="full"
            >
              Cancel Update
            </Button>
            <Button
              size="sm"
              colorScheme="green"
              onClick={handleBillCart}
              borderRadius="full"
            >
              Update Tab
            </Button>
          </>
        ) : (
          <>
            {" "}
            <Button
              size="sm"
              colorScheme="red"
              onClick={handleClearCart}
              borderRadius="full"
            >
              Clear
            </Button>
            {hasPermission(me?.permissions, [
              "POS>Hold Order",
              "POS>Employee Order",
            ]) && (
              <Button
                size="sm"
                colorScheme="blue"
                onClick={handleBillCart}
                borderRadius="full"
              >
                Bill
              </Button>
            )}
            <Button
              size="sm"
              colorScheme="green"
              onClick={handlePayedCart}
              borderRadius="full"
            >
              Payed
            </Button>
          </>
        )}
      </Flex>
    </Box>
  );
};

export default Cart;
