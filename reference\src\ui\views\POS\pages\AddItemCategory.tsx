/* eslint-disable @typescript-eslint/no-explicit-any */
import {
  Box,
  Button,
  Flex,
  FormControl,
  FormErrorMessage,
  FormLabel,
  Input,
  Text,
  useColorModeValue,
  useToast,
} from "@chakra-ui/react";
import { useContext, useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import {
  Category,
  useAddCategoryWithTypeNameMutation,
  useDeleteCategoryMutation,
  useGetCategoriesQuery,
} from "../../../generated/graphql";
import DeleteConfirm from "../../../components/DeleteConfirmation";
import { MeContext } from "../../../components/Wrapper";
import { hasPermission } from "../../../interfaces/Helpers";

const AddItemCategory = () => {
  const toast = useToast({ position: "top" });
  const me = useContext(MeContext);

  const [categories, setCategories] = useState<Category[]>([]);
  const [error, setError] = useState("");
  const [openDelete, setOpenDelete] = useState({ open: false, id: -1000000 });
  const [isEdit, setIsEdit] = useState(false);
  const [catToEdit, setCatToEdit] = useState<Category | null>(null);

  const [, addCategoryWithTypeName] = useAddCategoryWithTypeNameMutation();
  const [, deleteCategory] = useDeleteCategoryMutation();
  const [{ data, fetching }] = useGetCategoriesQuery({
    variables: { type: "Item_Category" },
    requestPolicy: "network-only",
  });

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors, isSubmitting },
  } = useForm();

  const textColor = useColorModeValue("gray.700", "white");
  const bgForm = useColorModeValue("white", "navy.800");

  useEffect(() => {
    if (data?.getCategories) {
      setCategories(data.getCategories as Category[]);
    }
  }, [data]);

  const onSubmit = async (values: any) => {
    if (isEdit && catToEdit) {
      const updatedCategories = categories.map((cat) =>
        cat.id === catToEdit.id ? { ...cat, name: values.name } : cat
      );
      setCategories(updatedCategories);
      setIsEdit(false);
      setCatToEdit(null);
      reset();
      toast({
        title: "Category edited successfully!",
        variant: "left-accent",
        status: "success",
        isClosable: true,
      });
    } else {
      const newCategory = await addCategoryWithTypeName({
        args: { name: values.name, typeName: "Item_Category" },
      });
      if (newCategory.error) setError(newCategory.error.message);
      else if (newCategory.data?.addCategoryWithTypeName.error)
        setError(newCategory.data?.addCategoryWithTypeName.error.message);
      else {
        setCategories([
          newCategory.data!.addCategoryWithTypeName.category! as Category,
          ...categories,
        ]);
        reset();
        toast({
          title: "Category added successfully!",
          variant: "left-accent",
          status: "success",
          isClosable: true,
        });
      }
    }
  };

  const handleDeleteCategory = async (id: number) => {
    await deleteCategory({ id });
    setOpenDelete({ open: false, id: -1000000 });
    setCategories(categories.filter((cat) => cat.id !== id));
    toast({
      title: "Category deleted successfully!",
      variant: "left-accent",
      status: "success",
      isClosable: true,
    });
  };

  return (
    <Flex
      direction="column"
      pt={{ base: "120px", md: "75px" }}
      flex="1" // Take remaining vertical space
      overflowY="auto"
      maxHeight={{ base: "calc(100vh - 0px)", md: "calc(100vh - 0px)" }}
      sx={{
        "&::-webkit-scrollbar": {
          width: "1px",
        },
        "&::-webkit-scrollbar-thumb": {
          background: "gray.300",
          borderRadius: "4px",
        },
        "&::-webkit-scrollbar-thumb:hover": {
          background: "gray.500",
        },
      }}
    >
      <DeleteConfirm
        loading={false} // Update if needed
        item="Category"
        open={openDelete.open}
        id={openDelete.id!}
        feedback={handleDeleteCategory}
        nofeedback={() => setOpenDelete({ open: false, id: -1000000 })}
      />

      {hasPermission(me?.permissions, [
        "Categories>Add",
        "Categories>Edit",
        "Categories>Delete",
      ]) && (
        <Box width="70%" mx="auto" mb={10}>
          <Flex
            direction="column"
            bg={bgForm}
            p="40px"
            borderRadius="15px"
            boxShadow="lg"
          >
            <Text
              fontSize="xl"
              color={textColor}
              fontWeight="bold"
              textAlign="center"
              mb="22px"
            >
              {isEdit ? "EDIT CATEGORY" : "ADD NEW CATEGORY"}
            </Text>

            {error && (
              <Text mb="10px" color="red.500" textColor="red.300">
                {error}
              </Text>
            )}

            <form onSubmit={handleSubmit(onSubmit)}>
              <FormControl mb={5} isInvalid={!!errors.name}>
                <FormLabel htmlFor="name">Category name</FormLabel>
                <Input
                  variant="filled"
                  fontSize="sm"
                  type="text"
                  placeholder="Category Name"
                  mb="4px"
                  size="lg"
                  id="name"
                  {...register("name", { required: "This is required" })}
                  defaultValue={isEdit ? catToEdit?.name : ""}
                />
                <FormErrorMessage>
                  {errors.name && (errors.name.message as any)}
                </FormErrorMessage>
              </FormControl>

              <Button
                fontSize="14px"
                variant="solid"
                fontWeight="bold"
                w="100%"
                h="45px"
                mb="24px"
                colorScheme="teal"
                isLoading={isSubmitting}
                type="submit"
              >
                {isEdit ? "Edit Category" : "Add Category"}
              </Button>

              {isEdit && (
                <Button
                  fontSize="14px"
                  fontWeight="bold"
                  w="100%"
                  h="45px"
                  mb="24px"
                  backgroundColor="darkkhaki"
                  color="white"
                  type="reset"
                  onClick={() => {
                    setIsEdit(false);
                    setCatToEdit(null);
                    reset();
                  }}
                >
                  Cancel Edit
                </Button>
              )}
            </form>
          </Flex>
        </Box>
      )}

      {categories.length > 0 && (
        <Box width="70%" mx="auto">
          <Flex
            direction="column"
            bg={bgForm}
            p="20px"
            borderRadius="15px"
            boxShadow="lg"
          >
            <Text fontSize="lg" color={textColor} fontWeight="bold" mb="20px">
              Categories
            </Text>
            {fetching ? (
              <Text>Loading categories...</Text>
            ) : (
              categories.map((category) => (
                <Box
                  key={category.id}
                  p="10px"
                  bg="gray.100"
                  borderRadius="8px"
                  mb="10px"
                >
                  <Flex justify="space-between" align="center">
                    <Text>{category.name}</Text>
                    <Flex gap="10px">
                      {hasPermission(me?.permissions, [
                        "Categories>Edit",
                        "Categories>Delete",
                      ]) && (
                        <Button
                          size="sm"
                          colorScheme="blue"
                          onClick={() => {
                            setIsEdit(true);
                            setCatToEdit(category);
                          }}
                        >
                          Edit
                        </Button>
                      )}
                      {hasPermission(me?.permissions, [
                        "Categories>Delete",
                      ]) && (
                        <Button
                          size="sm"
                          colorScheme="red"
                          onClick={() =>
                            setOpenDelete({ open: true, id: category.id })
                          }
                        >
                          Delete
                        </Button>
                      )}
                    </Flex>
                  </Flex>
                </Box>
              ))
            )}
          </Flex>
        </Box>
      )}
    </Flex>
  );
};

export default AddItemCategory;
