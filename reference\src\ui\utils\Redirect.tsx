/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useEffect } from "react";
import { Redirect } from "react-router-dom";
import { useMeQuery } from "../generated/graphql";

interface IRRProps {
  from: string;
  to: string;
}

const RedirectRoute: React.FC<IRRProps> = ({ to }: any) => {
  const [{ data, fetching }] = useMeQuery();
  useEffect(() => {
    console.log("Checking if authenticated.");
  }, [data, fetching]);
  return <Redirect from="/" to={"/" + data?.me?.role!.name + to} />;
};

export default RedirectRoute;
