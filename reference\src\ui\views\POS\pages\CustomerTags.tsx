/* eslint-disable @typescript-eslint/no-explicit-any */
import {
  Box,
  Button,
  Flex,
  FormControl,
  FormErrorMessage,
  FormLabel,
  Input,
  Text,
  useColorModeValue,
  useToast,
} from "@chakra-ui/react";
import { useContext, useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import {
  Category,
  useAddCategoryWithTypeNameMutation,
  useDeleteCategoryMutation,
  useGetCategoriesQuery,
} from "../../../generated/graphql";
import DeleteConfirm from "../../../components/DeleteConfirmation";
import { MeContext } from "../../../components/Wrapper";
import { hasPermission } from "../../../interfaces/Helpers";

const CustomerTags = () => {
  const toast = useToast({ position: "top" });

  const [categories, setCategories] = useState<Category[]>([]);
  const [error, setError] = useState("");
  const [openDelete, setOpenDelete] = useState({ open: false, id: -1000000 });
  const [isEdit, setIsEdit] = useState(false);
  const [catToEdit, setCatToEdit] = useState<Category | null>(null);

  const [, addCategoryWithTypeName] = useAddCategoryWithTypeNameMutation();
  const [, deleteCategory] = useDeleteCategoryMutation();
  const [{ data, fetching }] = useGetCategoriesQuery({
    variables: { type: "Customer_Tag" },
    requestPolicy: "network-only",
  });

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors, isSubmitting },
  } = useForm();
  const me = useContext(MeContext);

  const textColor = useColorModeValue("gray.700", "white");
  const bgForm = useColorModeValue("white", "navy.800");

  useEffect(() => {
    if (data?.getCategories) {
      setCategories(data.getCategories as Category[]);
    }
  }, [data]);

  const onSubmit = async (values: any) => {
    if (isEdit && catToEdit) {
      const updatedCategories = categories.map((cat) =>
        cat.id === catToEdit.id ? { ...cat, name: values.name } : cat
      );
      setCategories(updatedCategories);
      setIsEdit(false);
      setCatToEdit(null);
      reset();
      toast({
        title: "Customer tag edited successfully!",
        variant: "left-accent",
        status: "success",
        isClosable: true,
      });
    } else {
      const newCategory = await addCategoryWithTypeName({
        args: { name: values.name, typeName: "Customer_Tag" },
      });
      if (newCategory.error) setError(newCategory.error.message);
      else if (newCategory.data?.addCategoryWithTypeName.error)
        setError(newCategory.data?.addCategoryWithTypeName.error.message);
      else {
        setCategories([
          newCategory.data!.addCategoryWithTypeName.category! as Category,
          ...categories,
        ]);
        reset();
        toast({
          title: "Customer tag added successfully!",
          variant: "left-accent",
          status: "success",
          isClosable: true,
        });
      }
    }
  };

  const handleDeleteCategory = async (id: number) => {
    await deleteCategory({ id });
    setOpenDelete({ open: false, id: -1000000 });
    setCategories(categories.filter((cat) => cat.id !== id));
    toast({
      title: "Customer tag deleted successfully!",
      variant: "left-accent",
      status: "success",
      isClosable: true,
    });
  };

  return (
    <Flex
      direction="column"
      pt={{ base: "80px", md: "35px" }}
      flex="1" // Take remaining vertical space
      overflowY="auto"
      maxHeight={{ base: "calc(100vh - 0px)", md: "calc(100vh - 0px)" }}
      sx={{
        "&::-webkit-scrollbar": {
          width: "1px",
        },
        "&::-webkit-scrollbar-thumb": {
          background: "gray.300",
          borderRadius: "4px",
        },
        "&::-webkit-scrollbar-thumb:hover": {
          background: "gray.500",
        },
      }}
    >
      <DeleteConfirm
        loading={false} // Update if needed
        item="Customer Tag"
        open={openDelete.open}
        id={openDelete.id!}
        feedback={handleDeleteCategory}
        nofeedback={() => setOpenDelete({ open: false, id: -1000000 })}
      />

      {hasPermission(me?.permissions, [
        "Customer Tags>Add",
        "Customer Tags>Edit",
        "Customer Tags>Delete",
      ]) && (
        <Box width="70%" mx="auto" mb={10}>
          <Flex
            direction="column"
            bg={bgForm}
            p="40px"
            borderRadius="15px"
            boxShadow="lg"
          >
            <Text
              fontSize="xl"
              color={textColor}
              fontWeight="bold"
              textAlign="center"
              mb="22px"
            >
              {isEdit ? "EDIT CUSTOMER TAG" : "ADD NEW CUSTOMER TAG"}
            </Text>

            {error && (
              <Text mb="10px" color="red.500" textColor="red.300">
                {error}
              </Text>
            )}

            <form onSubmit={handleSubmit(onSubmit)}>
              <FormControl mb={5} isInvalid={!!errors.name}>
                <FormLabel htmlFor="name">Tag name</FormLabel>
                <Input
                  variant="filled"
                  fontSize="sm"
                  type="text"
                  placeholder="Tag Name"
                  mb="4px"
                  size="lg"
                  id="name"
                  {...register("name", { required: "This is required" })}
                  defaultValue={isEdit ? catToEdit?.name : ""}
                />
                <FormErrorMessage>
                  {errors.name && (errors.name.message as any)}
                </FormErrorMessage>
              </FormControl>

              <Button
                fontSize="14px"
                variant="solid"
                fontWeight="bold"
                w="100%"
                h="45px"
                mb="24px"
                colorScheme="teal"
                isLoading={isSubmitting}
                type="submit"
              >
                {isEdit ? "Edit Customer Tag" : "Add Customer Tag"}
              </Button>

              {isEdit && (
                <Button
                  fontSize="14px"
                  fontWeight="bold"
                  w="100%"
                  h="45px"
                  mb="24px"
                  backgroundColor="darkkhaki"
                  color="white"
                  type="reset"
                  onClick={() => {
                    setIsEdit(false);
                    setCatToEdit(null);
                    reset();
                  }}
                >
                  Cancel Edit
                </Button>
              )}
            </form>
          </Flex>
        </Box>
      )}
      {categories.length > 0 && (
        <Box width="70%" mx="auto">
          <Flex
            direction="column"
            bg={bgForm}
            p="20px"
            borderRadius="15px"
            boxShadow="lg"
          >
            <Text fontSize="lg" color={textColor} fontWeight="bold" mb="20px">
              Customer Tags
            </Text>
            {fetching ? (
              <Text>Loading customer tags...</Text>
            ) : (
              categories.map((category) => (
                <Box
                  key={category.id}
                  p="10px"
                  bg="gray.100"
                  borderRadius="8px"
                  mb="10px"
                >
                  <Flex justify="space-between" align="center">
                    <Text>{category.name}</Text>
                    <Flex gap="10px">
                      {hasPermission(me?.permissions, [
                        "Customer Tags>Edit",
                        "Customer Tags>Delete",
                      ]) && (
                        <Button
                          size="sm"
                          colorScheme="blue"
                          onClick={() => {
                            setIsEdit(true);
                            setCatToEdit(category);
                          }}
                        >
                          Edit
                        </Button>
                      )}
                      {hasPermission(me?.permissions, [
                        "Customer Tags>Delete",
                      ]) && (
                        <Button
                          size="sm"
                          colorScheme="red"
                          onClick={() =>
                            setOpenDelete({ open: true, id: category.id })
                          }
                        >
                          Delete
                        </Button>
                      )}
                    </Flex>
                  </Flex>
                </Box>
              ))
            )}
          </Flex>
        </Box>
      )}
    </Flex>
  );
};

export default CustomerTags;
